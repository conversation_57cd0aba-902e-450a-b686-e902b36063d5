 import { ChangeDetectorRef, Component, OnInit, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { GlobalService } from '../../shared/data/global.service';
import { MsxView } from 'app/shared/components/msx-view/models/MsxView';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { WidgetType } from 'app/shared/components/msx-view/models/WidgetType';
import { Act } from 'app/shared/components/msx-card-datatable/enums/act';
import { ViewDocumentRequest } from 'app/model/api/view.document.request';
import { ViewDocumentResponse } from 'app/model/api/view.document.response';
import { HttpClient } from '@angular/common/http';
import { Document } from 'app/model/document';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { SignerComponent } from './modal/signer/signer.component';
import { FormConstant } from 'app/shared/components/ms-form/constants/form.constant';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { InquirySearchFilterBranchManager, InquirySearchFilterCustomer, InquiryTableAdminCredit, InquiryTableBranchManager, InquiryTableBranchManagerMobile, InquiryTableCustomer, InquiryTableCustomerMobile } from './view/inquiry-list.view';
import { UserProfile } from 'app/model/user-profile';
import * as swalFunction from '../../shared/data/sweet-alerts';
import { DeviceDetectorService } from 'ngx-device-detector';
import { ToastrService } from 'ngx-toastr';
import { TenantSettingRequest } from 'app/shared/dto/tenant-setting/tenant-setting.request';
import { AuditContext } from 'app/model/audit.context'; 
import { QuestionDropdown } from 'app/shared/components/ms-form/questions';
import { DocumentService } from 'app/services/api/document.service';
import { InquiryRequest } from 'app/shared/dto/inquiry/inquiry.request';
import { saveAs } from 'file-saver';
import { StartStampingRequestEmbed } from 'app/shared/dto/inquiry/start-stamping-embed.request';
import { BaseResponse } from 'app/shared/dto/base.response';
import { ColumnType } from 'app/shared/components/msx-card-datatable/enums/column-type';

@Component({
  selector: 'app-inquiry',
  templateUrl: './inquiry.component.html',
  styleUrls: ['./inquiry.component.scss',],
  encapsulation: ViewEncapsulation.None
})
export class InquiryComponent implements OnInit {

  view: MsxView;
  serviceUrl = URLConstant.InquiryDocument;

  public params;
  private pathSrc: string;
  isMonitoring: boolean;
  msg: string;
  tenantCode: string;
  isMobile = false;
  isHO: String;
  refNumLabel: string;
  customerColumnLength = InquiryTableCustomer.columns.length;
  bmColumntLength = InquiryTableBranchManager.columns.length;
  admCredColumnLength = InquiryTableAdminCredit.columns.length;

  swal = swalFunction;

  constructor(private router: Router, private activeRoute: ActivatedRoute, public global: GlobalService,
    private cdr: ChangeDetectorRef, private http: HttpClient,
    private modalService: NgbModal, private deviceService: DeviceDetectorService,
    private toastrService: ToastrService, private documentService: DocumentService) {
    if (deviceService.isMobile()) {
      this.isMobile = true;
    }
    const snapshot = this.activeRoute.snapshot.data;
    this.pathSrc = snapshot.path;
    this.activeRoute.queryParams.subscribe(params => {
      if (params['msg']) {
        this.msg = params['msg'];
      }

      if (params['tenantCode'] && this.pathSrc === CommonConstant.PATH_SRC_EMBED_V2) {
        this.tenantCode = params['tenantCode'];
      }

      if (params['isMonitoring']) {
        this.isMonitoring = JSON.parse(params['isMonitoring']);
        console.log('IsMonitoring', this.isMonitoring);
      }

      if (params['isHO']) {
        this.isHO = params['isHO'];
      }
      console.log('isHO', this.isHO);
    });
  }

  async ngOnInit() {
    
    localStorage.removeItem('msg');
    if (this.pathSrc === CommonConstant.PATH_SRC_EMBED || this.pathSrc === CommonConstant.PATH_SRC_EMBED_V2) {
      localStorage.removeItem('token');
      localStorage.clear();
    }

    if (this.msg && this.isMonitoring !== undefined) {
      const user: UserProfile = new UserProfile();
      user.loginId = 'CONFINS';
      user.role = {
        tenantCode: '',
        tenantName: '',
        roleCode: '',
        roleName: ''
      }
      user.roles = [{
        tenantCode: '',
        tenantName: '',
        roleCode: '',
        roleName: ''
      }]
      this.global.user = user;
      this.global.msg = this.msg;
      this.serviceUrl = URLConstant.InquiryDocumentEmbed;

      localStorage.clear();
    } 
    if (this.msg && this.isMonitoring !== undefined && this.tenantCode && this.pathSrc === CommonConstant.PATH_SRC_EMBED_V2) {
      const user: UserProfile = new UserProfile();
      user.loginId = 'CONFINS';
      user.role = {
        tenantCode: this.tenantCode,
        tenantName: '',
        roleCode: '',
        roleName: ''
      }
      user.roles = [{
        tenantCode: this.tenantCode,
        tenantName: '',
        roleCode: '',
        roleName: ''
      }]
      this.global.user = user;
      this.global.msg = this.msg;
      this.serviceUrl = URLConstant.InquiryDocumentEmbedV2;

      localStorage.clear();
    } 

    await this.getRefNumLabel();

    if (this.global.user !== null) {
      if (this.global.user.role.roleCode === CommonConstant.CUSTOMER ||
        this.global.user.role.roleCode === CommonConstant.GUARANTOR ||
        this.global.user.role.roleCode === CommonConstant.SPOUSE) {
        this.initCustomer();
      } else if (this.global.user.role.roleCode === CommonConstant.BRANCH_MANAGER ||
        this.isMonitoring !== undefined) {
        this.initBranchManager();
      }
    } 
    console.log(this.global.user.role.roleCode);
    console.log('xx this.view', this.view);
  }

  initCustomer() {
    this.customerColumnLength = this.isMobile ? InquiryTableCustomerMobile.columns.length : InquiryTableCustomer.columns.length;
    InquirySearchFilterCustomer.params = [];
    InquirySearchFilterCustomer.params.push(
      {
        key: 'inquiryType',
        controlType: FormConstant.TYPE_HIDDEN,
        value: CommonConstant.INQUIRY_TYPE_LIST
      }
    );
    InquirySearchFilterCustomer.params.push(
      {
        key: 'page',
        controlType: FormConstant.TYPE_HIDDEN,
        value: 1
      }
    );
    InquirySearchFilterCustomer.params.push(
      {
        key: 'tenantCode',
        controlType: FormConstant.TYPE_HIDDEN,
        value: this.global.user.role.tenantCode
      }
    );
    InquirySearchFilterCustomer.params.push(
      {
        key: 'audit',
        controlType: FormConstant.TYPE_HIDDEN,
        value: {
          callerId: this.global.user.loginId
        }
      },
    );

    if (this.customerColumnLength < 9) {
      if (this.isMobile) {
        this.customerColumnLength = InquiryTableCustomerMobile.columns.unshift(
          {
            type: ColumnType.Text,
            cardType: ColumnType.Title,
            prop: 'refNumber',
            label: this.refNumLabel,
            width: 140
          }
        );
      } else {
        this.customerColumnLength = InquiryTableCustomer.columns.unshift(
          {
            type: ColumnType.Text,
            prop: 'refNumber',
            label: this.refNumLabel,
            width: 140
          }
        );
      }
    }

    this.view = {
      title: CommonConstant.TITLE_INQUIRY_DOCUMENT,
      isCard: true,
      components: [
        {
          type: WidgetType.SearchFilter,
          component: InquirySearchFilterCustomer
        },
        {
          type: WidgetType.Datatable,
          component: this.isMobile ? InquiryTableCustomerMobile : InquiryTableCustomer
        }
      ]
    }

    if (this.serviceUrl === URLConstant.InquiryDocument) {
      const roleCodeParam = {
        key: 'roleCode',
        controlType: FormConstant.TYPE_HIDDEN,
        value: this.global.user.role.roleCode
      };
      this.view.components[0].component.params.push(roleCodeParam);
    } 
  }

  initBranchManager() {
    this.bmColumntLength = this.isMobile ? InquiryTableBranchManagerMobile.columns.length : InquiryTableBranchManager.columns.length;
    InquirySearchFilterBranchManager.params = [];


    InquirySearchFilterBranchManager.params.push(
      {
        key: 'inquiryType',
        controlType: FormConstant.TYPE_HIDDEN,
        value: CommonConstant.INQUIRY_TYPE_LIST
      }
    );
    InquirySearchFilterBranchManager.params.push(
      {
        key: 'page',
        controlType: FormConstant.TYPE_HIDDEN,
        value: 1
      }
    );
    InquirySearchFilterBranchManager.params.push(
      {
        key: 'audit',
        controlType: FormConstant.TYPE_HIDDEN,
        value: {
          callerId: this.global.user.loginId
        }
      },
    );

    if (this.msg) {
      InquirySearchFilterBranchManager.params.push(
        {
          key: 'msg',
          controlType: FormConstant.TYPE_HIDDEN,
          value: this.msg
        }
      );
    } else if (this.msg && this.pathSrc === CommonConstant.PATH_SRC_EMBED_V2) {
      InquirySearchFilterBranchManager.params.push(
        {
          key: 'msg',
          controlType: FormConstant.TYPE_HIDDEN,
          value: this.msg
        }
      );
      InquirySearchFilterBranchManager.params.push(
        {
          key: 'tenantCode',
          controlType: FormConstant.TYPE_HIDDEN,
          value: this.global.user.role.tenantCode
        }
      );
    }
     else {
      InquirySearchFilterBranchManager.params.push(
        {
          key: 'tenantCode',
          controlType: FormConstant.TYPE_HIDDEN,
          value: this.global.user.role.tenantCode
        }
      );
    }

    if (this.isMonitoring) {
      InquirySearchFilterBranchManager.params.push(
        {
          key: 'isMonitoring',
          controlType: FormConstant.TYPE_HIDDEN,
          value: this.isMonitoring
        }
      );
    }

    if (this.global.user.role.roleCode === CommonConstant.BRANCH_MANAGER || !this.isMonitoring) {
      if (this.bmColumntLength < 10) {
        if (this.isMobile) {
          this.bmColumntLength = InquiryTableBranchManagerMobile.columns.unshift(
            {
              type: ColumnType.Text,
              cardType: ColumnType.Title,
              prop: 'refNumber',
              label: this.refNumLabel,
              width: 120
            }
          );
        } else {
          this.bmColumntLength = InquiryTableBranchManager.columns.unshift(
            {
              type: ColumnType.Text,
              prop: 'refNumber',
              label: this.refNumLabel,
              width: 120
            }
          );
        }
      } 
    } else {
      if (this.admCredColumnLength < 10) {
        InquiryTableAdminCredit.columns.unshift(
          {
            type: ColumnType.Text,
            cardType: ColumnType.Title,
            prop: 'refNumber',
            label: this.refNumLabel,
            width: 120
          }
        );
      } 
      
    }

    if (this.msg && this.isHO === '1') {
      InquirySearchFilterBranchManager.exportExcel = true;
      InquirySearchFilterBranchManager.params.push(
        {
          key: 'isHO',
          controlType: FormConstant.TYPE_HIDDEN,
          value: this.isHO
        }
      )
    }

    if (this.msg && this.isHO === '1' && this.admCredColumnLength < 10) {
      let urlRegion, urlOffice, tenant;
      if (this.msg && this.router.url.includes('/embed/') && !this.router.url.includes('/V2/')){
        tenant = this.global.user.role.tenantCode;
        urlRegion = URLConstant.RegionListEmbed;
        urlOffice = URLConstant.OfficeListEmbed
      } else if(this.msg && this.router.url.includes('/embed/') && this.router.url.includes('/V2/')){
        tenant = this.tenantCode;
        urlRegion = URLConstant.RegionListEmbedV2;
        urlOffice = URLConstant.OfficeListEmbedV2;
      }
      
      InquirySearchFilterBranchManager.components.push(
        new QuestionDropdown({
          key: 'regionCode',
          label: 'Region',
          placeholder: 'Select Region',
          serviceUrl: urlRegion,
          options: [
            {key: '', value: 'All'}
          ],
          args: {
            list: 'regionList',
            key: 'regionCode',
            value: 'regionName'
          },
          params: {
            tenantCode: tenant,
            msg: this.msg
          },
          value: ''
        })
      )

      InquirySearchFilterBranchManager.components.push(
        new QuestionDropdown({
          key: 'officeCode',
          label: 'Office',
          placeholder: 'Select Office',
          serviceUrl: urlOffice,
          options: [
            {key: '', value: 'All'}
          ],
          args: {
            list: 'officeList',
            key: 'officeCode',
            value: 'officeName'
          },
          params: {
            tenantCode: tenant,
            msg: this.msg
          },
          value: ''
        })
      )

      InquirySearchFilterBranchManager.components.push(
        new QuestionDropdown({
          key: 'prosesMaterai',
          label: CommonConstant.LABEL_STAMPING_PROCESS,
          placeholder: 'Select Stamping Process',
          options: [
          {key: '', value: 'All'},
          {key: 'Not Started', value: 'Not Started'},
          {key: 'Failed', value: 'Failed'},
          {key: 'In Progress', value: 'In Progress'},
          {key: 'Success', value: 'Success'},
        ],
        })
      )

      if (this.msg && this.router.url.includes('/V2/')) {
        InquirySearchFilterBranchManager.components.push(
          new QuestionDropdown({
              key: 'isActive',
              label: 'Status',
              controlType: FormConstant.TYPE_DROPDOWN,
              options: [
                {key: '', value: 'All'},
                {key: '1', value: 'Active'},
                {key: '0', value: 'Inactive'}
              ],
              value: ''
            })
        )

        InquiryTableAdminCredit.columns.push(
          {
            type: ColumnType.IsActive,
            cardType: ColumnType.Detail,
            prop: 'isActive',
            label: 'Status',
            width: 80
          },
        )
      }

      InquiryTableBranchManagerMobile.columns.push({
        type: ColumnType.Text,
        cardType: ColumnType.Detail,
        prop: 'officeName',
        label: 'Office',
        width: 120
      })
      InquiryTableBranchManagerMobile.columns.push({
        type: ColumnType.Text,
        cardType: ColumnType.Detail,
        prop: 'regionName',
        label: 'Region',
        width: 120
      })
      InquiryTableBranchManagerMobile.columns.push({
        type: ColumnType.Text,
        cardType: ColumnType.Detail,
        prop: 'statusProsesMaterai',
        label: CommonConstant.LABEL_STAMPING_PROCESS,
        width: 120
      })
      InquiryTableBranchManager.columns.push({
        type: ColumnType.Text,
        prop: 'officeName',
        label: 'Office',
        width: 120
      })
      InquiryTableBranchManager.columns.push({
        type: ColumnType.Text,
        prop: 'regionName',
        label: 'Region',
        width: 120
      })
      InquiryTableBranchManager.columns.push({
        type: ColumnType.Text,
        prop: 'statusProsesMaterai',
        label: CommonConstant.LABEL_STAMPING_PROCESS,
        width: 120
      })
      InquiryTableAdminCredit.columns.push({
        type: ColumnType.Action,
        label: 'Action',
        width: 140,
        action: [
          {
            class: CommonConstant.TEXT_PRIMARY,
            icon: 'ft-eye',
            type: Act.View,
            descr: 'View',
            cardAction: true,
            mobileMode: false, 
          },
          {
            class: CommonConstant.TEXT_PRIMARY,
            icon: CommonConstant.ICON_FT_DOWNLOAD,
            type: Act.Download,
            descr: 'Download',
            cardAction: false,
            mobileMode: true,
            iconSrc: CommonConstant.ICON_SRC_DOWNLOAD
          },
          {
            class: CommonConstant.TEXT_PRIMARY,
            icon: 'ft-user',
            type: Act.ViewSigner,
            descr: 'View Signer',
            cardAction: false,
            mobileMode: true,
            iconSrc: CommonConstant.ICON_SRC_USER
          },
          {
            class: CommonConstant.TEXT_PRIMARY,
            type: Act.Resend,
            icon: 'ft-mail',
            condition: true,
            conditionedClass: 'd-none',
            conditionVariable: 'signStatus',
            conditionExpected: 'Complete',
            descr: 'Resend Notification',
            cardAction: false,
            mobileMode: true,
            iconSrc: CommonConstant.ICON_SRC_MAIL
          },
          {
            class: CommonConstant.TEXT_DANGER,
            icon: 'ft-x-circle',
            type: Act.Cancel,
            condition: true,
            conditionedClass: 'd-none',
            conditionVariable: 'isActive',
            conditionExpected: '0',
            descr: 'Cancel Digital Sign',
            cardAction: false,
            mobileMode: true,
            iconSrc: CommonConstant.ICON_SRC_CANCEL
          },
          {
            class: CommonConstant.TEXT_PRIMARY,
            icon: 'ft-play-circle',
            descr: 'Start Stamping',
            type: Act.StartStamping,
            condition: true,
            conditionVariable: 'canStartStamp',
            conditionExpected: '0',
            conditionedClass: 'd-none',
            cardAction: false,
            mobileMode: true,
            iconSrc: CommonConstant.ICON_SRC_PLAY
          },
        ]
      })
      InquiryTableAdminCredit.columns.push({
        type: ColumnType.Text,
        cardType: ColumnType.Detail,
        prop: 'officeName',
        label: 'Office',
        width: 120
      })
      InquiryTableAdminCredit.columns.push({
        type: ColumnType.Text,
        cardType: ColumnType.Detail,
        prop: 'regionName',
        label: 'Region',
        width: 120
      })
      InquiryTableAdminCredit.columns.push({
        type: ColumnType.Text,
        cardType: ColumnType.Detail,
        prop: 'statusProsesMaterai',
        label: CommonConstant.LABEL_STAMPING_PROCESS,
        width: 120
      })
    } 
    
    if  (this.msg && this.isHO === '0' && this.admCredColumnLength < 10) {
      if (this.msg && this.router.url.includes('/V2/')) {
        InquirySearchFilterBranchManager.components.push(
          new QuestionDropdown({
              key: 'isActive',
              label: 'Status',
              controlType: FormConstant.TYPE_DROPDOWN,
              options: [
                {key: '', value: 'All'},
                {key: '1', value: 'Active'},
                {key: '0', value: 'Inactive'}
              ],
              value: ''
            })
        )

        InquiryTableAdminCredit.columns.push(
          {
            type: ColumnType.IsActive,
            cardType: ColumnType.Detail,
            prop: 'isActive',
            label: 'Status',
            width: 80
          },
        )
      }

      InquiryTableAdminCredit.columns.push({
        type: ColumnType.Action,
        label: 'Action',
        width: 140,
        action: [
          {
            class: CommonConstant.TEXT_PRIMARY,
            icon: 'ft-eye',
            type: Act.View,
            descr: 'View',
            cardAction: true,
            mobileMode: false,
          },
          {
            class: CommonConstant.TEXT_PRIMARY,
            icon: CommonConstant.ICON_FT_DOWNLOAD,
            type: Act.Download,
            descr: 'Download',
            cardAction: false,
            mobileMode: true,
            iconSrc: CommonConstant.ICON_SRC_DOWNLOAD
          },
          {
            class: CommonConstant.TEXT_PRIMARY,
            icon: 'ft-user',
            type: Act.ViewSigner,
            descr: 'View Signer',
            cardAction: false,
            mobileMode: true,
            iconSrc: CommonConstant.ICON_SRC_USER
          },
          {
            class: CommonConstant.TEXT_PRIMARY,
            type: Act.Resend,
            icon: 'ft-mail',
            condition: true,
            conditionedClass: 'd-none',
            conditionVariable: 'signStatus',
            conditionExpected: 'Complete',
            descr: 'Resend Notification',
            cardAction: false,
            mobileMode: true,
            iconSrc: CommonConstant.ICON_SRC_MAIL
          },
          {
            class: CommonConstant.TEXT_DANGER,
            icon: 'ft-x-circle',
            type: Act.Cancel,
            condition: true,
            conditionedClass: 'd-none',
            conditionVariable: 'signStatus',
            conditionExpected: 'Complete',
            descr: 'Cancel Digital Sign',
            cardAction: false,
            mobileMode: true,
            iconSrc: CommonConstant.ICON_SRC_CANCEL
          },
          {
            class: CommonConstant.TEXT_PRIMARY,
            icon: 'ft-play-circle',
            descr: 'Start Stamping',
            type: Act.StartStamping,
            condition: true,
            conditionVariable: 'canStartStamp',
            conditionExpected: '0',
            conditionedClass: 'd-none',
            cardAction: false,
            mobileMode: true,
            iconSrc: CommonConstant.ICON_SRC_PLAY
          },
        ]
      })
    }

    var title = (this.global.user.role.roleCode === CommonConstant.BRANCH_MANAGER  || !this.isMonitoring) ? CommonConstant.TITLE_INQUIRY_DOCUMENT : 'Monitoring Document';

    this.view = {
      title: (this.global.user.role.roleCode === CommonConstant.BRANCH_MANAGER  || !this.isMonitoring) ? CommonConstant.TITLE_INQUIRY_DOCUMENT : 'Monitoring Document',
      isCard: true,
      components: [
        {
          type: WidgetType.SearchFilter,
          component: InquirySearchFilterBranchManager
        },
        {
          type: WidgetType.Datatable,
          component: (this.global.user.role.roleCode === CommonConstant.BRANCH_MANAGER || !this.isMonitoring) ? (this.isMobile ? InquiryTableBranchManagerMobile : InquiryTableBranchManager) : InquiryTableAdminCredit
        }
      ]
    } 

    if (this.serviceUrl === URLConstant.InquiryDocument) {
      const roleCodeParam = {
        key: 'roleCode',
        controlType: FormConstant.TYPE_HIDDEN,
        value: this.global.user.role.roleCode
      };
      this.view.components[0].component.params.push(roleCodeParam);
    }
  }

  onItemClickListener(event) {
    const data = event['data'];

    switch (event['act']['type']) {
      case Act.ViewSigner:
        return this.openSignerListModal(data);

      case Act.Download:
        return this.gotoViewOrDownload(data, true);

      case Act.Cancel:
        return this.cancelDigitalSign(data);

      case Act.Resend:
        return this.resendNotification(data);
      case Act.StartStamping:
        return this.startDocumentStampingEmbed(data);
       
      default:
        return this.gotoViewOrDownload(data, false);
    }
  }

  startDocumentStampingEmbed(data) {
    this.swal.Confirm('Apakah Anda yakin ingin memulai stamping?').then(
      (result) => {
        if (result.isConfirmed) {
          const request = new StartStampingRequestEmbed();
          request.tenantCode = this.global.user.role.tenantCode;
          request.refNumber = data.refNumber;
          request.msg = this.msg;
          this.http.post<BaseResponse>(URLConstant.StartStampingDocumentEmbed, request).subscribe(
            (response) => {
              if (response.status.code === 0) {
                this.swal.Success('Proses stamping berhasil dimulai');
              }
            }
          )
        }
      }
    );
  }

  gotoViewOrDownload(data: Document, download: boolean) {
    console.log('Data', data);
    const request: ViewDocumentRequest = new ViewDocumentRequest();
    request.documentId = data.documentId;

    let url;
    if (this.msg && this.router.url.includes('/embed/') && !this.router.url.includes('/V2/')){
      url = URLConstant.ViewDocumentEmbed;
      request.msg = this.msg;
      if (this.isHO) {
        request.isHO = this.isHO.toString();
      }
      if (this.isMonitoring) {
        request.isMonitoring = true;
      }
    } else if(this.msg && this.router.url.includes('/embed/') && this.router.url.includes('/V2/')){
      url =URLConstant.ViewDocumentEmbedV2;
      request.msg = this.msg;
      request.tenantCode = this.tenantCode;
      if (this.isHO) {
        request.isHO = this.isHO.toString();
      }
      if (this.isMonitoring) {
        request.isMonitoring = true;
      }
    } else{
      url =URLConstant.ViewDocument;
    }

    //const url = (this.msg) ? URLConstant.ViewDocumentEmbed : URLConstant.ViewDocument;
    this.http.post<ViewDocumentResponse>(url, request).subscribe(
      (response) => {
        if (response.status.code === 0) {
          if (download) {
            const downloadLink = document.createElement('a');
            const fileName = data.docTemplateName + '.pdf';

            downloadLink.href = `data:application/pdf;base64,${response.pdfBase64}`;
            downloadLink.download = fileName;
            downloadLink.click();
          } else {
            let extras;
            if(this.msg){
              extras = { pdfBase64: response.pdfBase64, refNumber: data.refNumber,  msg: this.msg , tenantCode: this.tenantCode };
            } else{
              extras = { pdfBase64: response.pdfBase64, refNumber: data.refNumber };
            }
            
            data = { ...data, ...extras };

            if (this.pathSrc === CommonConstant.PATH_SRC_EMBED) {
              this.router.navigate([PathConstant.EMBED_VIEW_DOCUMENT_INQUIRY], { state: data });
            } else if (this.pathSrc === CommonConstant.PATH_SRC_EMBED_V2) {
              this.router.navigate([PathConstant.EMBED_V2_VIEW_DOCUMENT_INQUIRY], { state: data });
            } else {
              this.router.navigate([PathConstant.VIEW_DOCUMENT_INQUIRY], { state: data });
            }
          }
        }
      }
    );
  }

  openSignerListModal(data: Document) {
    const modal = this.modalService.open(SignerComponent, { size: 'lg' });
    modal.componentInstance.documentId = data.documentId;
    if (this.msg) {
      modal.componentInstance.msg = this.msg;
    } 
    if (this.tenantCode) {
      modal.componentInstance.tenantCode = this.tenantCode;
    }

    if (this.isMonitoring) {
      modal.componentInstance.isMonitoring = this.isMonitoring;
    }
  }

  cancelDigitalSign(data: Document) {
    let url;
    if(this.router.url.includes('/V2/')){
      url = URLConstant.CancelDigitalSignV2;
    } else{
      url = URLConstant.CancelDigitalSign;
    }

    const refNumber = data.refNumber;
    const confirmationText = 'Apakah Anda yakin ingin membatalkan ' + refNumber + '?';

    this.swal.Confirm(confirmationText).then(
      (result) => {
        if (result.isConfirmed === true) {
          this.http.post(url, { documentId: data.documentId, msg: this.msg, tenantCode: this.tenantCode }).subscribe(
            (response) => {
              if (response['status']['code'] !== 0) {
                console.log('CancelDigitalSign Error', response);
              } else {
                this.swal.SuccessWithRedirect('Dokumen berhasil dibatalkan', this.router.url);
              }
            }
          );
        }
      }
    );
  }

  async getRefNumLabel() {
    const tenantSettingReq: TenantSettingRequest = new TenantSettingRequest();
    let url;
    if (this.msg && !this.router.url.includes('/V2/')) {
      tenantSettingReq.msg = this.msg;
      url = URLConstant.GetTenantSettingEmbed;
    } else if(this.msg && this.router.url.includes('/V2/')){
      tenantSettingReq.msg = this.msg;
      tenantSettingReq.tenantCode = this.tenantCode;
      url = URLConstant.GetTenantSettingEmbedV2;
    } else {
      tenantSettingReq.tenantCode = this.global.user.role.tenantCode;
      url = URLConstant.GetTenantSetting;
    }
    tenantSettingReq.param = 'REF_NO_LABEL';
    await this.http.post(url, tenantSettingReq).toPromise().then(
      (response) => {
        if (response["status"]["code"] != 0) {
          this.toastrService.error(response["status"]["message"]);
        } else {
          this.refNumLabel = response["refNumberLabel"];
          if (this.refNumLabel.includes(":")) {
            this.refNumLabel = this.refNumLabel.substring(0,this.refNumLabel.length-2);
          }
        }
      }
    );
  }

  resendNotification(data: Document) {
    let url;
    if(this.router.url.includes('/V2/')){
      url = URLConstant.ResendNotificationV2;
    } else{
      url = URLConstant.ResendNotification;
    }

    this.swal.Confirm('Akan mengirimkan notifikasi tanda tangan ke user yang belum tanda tangan di dokumen dengan ref number ' + data.refNumber, 'Apakah Anda yakin?').then(
      (result) => {
        if (result.isConfirmed === true) {
          this.http.post(url, { documentId: data.documentId, msg: this.msg , tenantCode: this.tenantCode}).subscribe(
            (response) => {
              if (response['status']['code'] !== 0) {
                console.log('ResendNotification Error', response);
              } else {
                this.swal.Success('Resend Notification Sukses');
              }
            }
          );
        }
      }
    );
  }
  
  downloadDocumentReport(params) {

    if (!this.router.url.includes('/embed/')) {
      return;
    }

    console.log('download param', params);
    const request = new InquiryRequest();
    // Masih hardcode untuk Monitoring HO saja
    request.inquiryType = '';
    request.customerName = params['customerName'];
    request.refNumber = params['refNumber'];
    request.transactionStatus = params['transactionStatus'];
    request.docType = params['docType'];
    request.officeCode = params['officeCode'];
    request.regionCode = params['regionCode'];
    request.completedDateStart = this.dateToString(params['completedDateStart']);
    request.completedDateEnd = this.dateToString(params['completedDateEnd']);
    request.requestedDateStart = this.dateToString(params['requestedDateStart']);
    request.requestedDateEnd = this.dateToString(params['requestedDateEnd']);

    let url: string;
    if (this.router.url.includes('/embed/') && !this.router.url.includes('/V2/')){
      url = URLConstant.DownloadDocumentExcelEmbed;
      request.msg = this.msg;
      request.isHO = this.isHO.toString();
      request.isMonitoring = this.isMonitoring;
    } else if(this.msg && this.router.url.includes('/embed/') && this.router.url.includes('/V2/')){
      url = URLConstant.DownloadDocumentExcelEmbedV2;
      request.msg = this.msg;
      request.tenantCode = this.tenantCode;
      request.isHO = this.isHO.toString();
      request.isMonitoring = this.isMonitoring; 
      request.isActive = params['isActive'];
    }
    

    this.documentService.downloadDocumentExcelReport(request, url).subscribe(
      response => {
        if (response.status.code === 0) {
          const blob = this.b64toBlob(response.base64ExcelReport);
          saveAs(blob, response.filename);
        }
      }
    )
  }

  private dateToString(dateObject: any) {
    if (!dateObject) {
      return null;
    }
    return (dateObject['year']) ? dateObject['year'] + '-' + dateObject['month'] + '-' + dateObject['day'] : dateObject;
  }

  b64toBlob(b64Data, contentType = '', sliceSize = 512) {
    const byteCharacters = atob(b64Data);
    const byteArrays = [];

    for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
      const slice = byteCharacters.slice(offset, offset + sliceSize);

      const byteNumbers = new Array(slice.length);
      for (let i = 0; i < slice.length; i++) {
        byteNumbers[i] = slice.charCodeAt(i);
      }

      const byteArray = new Uint8Array(byteNumbers);
      byteArrays.push(byteArray);
    }

    const blob = new Blob(byteArrays, { type: contentType });
    return blob;
  }
}
