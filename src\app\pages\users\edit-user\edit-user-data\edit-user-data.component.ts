import { Location } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { EditUserDataRequest } from 'app/model/api/edit-user-data.request';
import { EditUserData } from 'app/model/edit-user-data';
import { IpService } from 'app/services/api/ip.service';
import { UserService } from 'app/services/api/user.service';
import { FormConstant } from 'app/shared/components/ms-form/constants/form.constant';
import { FormModel } from 'app/shared/components/ms-form/models';
import { MsxFormControlService } from 'app/shared/components/ms-form/msx-form-control.service';
import { QuestionDate, QuestionDropdown, QuestionTextbox } from 'app/shared/components/ms-form/questions';
import { QuestionBase } from 'app/shared/components/ms-form/questions/question-base';
import { QuestionTextarea } from 'app/shared/components/ms-form/questions/question-textarea';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { Success } from 'app/shared/data/sweet-alerts';
import * as moment from 'moment';

@Component({
  selector: 'app-edit-user-data',
  templateUrl: './edit-user-data.component.html',
  styleUrls: ['./edit-user-data.component.scss']
})
export class EditUserDataComponent implements OnInit {

  formObj: FormModel<any>;
  msxForm: FormGroup;
  userForm: FormGroup;
  questions: {[key: string]: QuestionBase<any>};
  data: EditUserData;
  formData: any;
  direction = FormConstant.DIRECTION_HORIZONTAL;
  statusIsActive: any;
  statusIsRegist: any;
  statusBoolean: boolean;
  ipAddress;

  constructor(private fcs: MsxFormControlService, private location: Location, private router: Router, 
    private userService: UserService, private formBuilder: FormBuilder, private ipService: IpService) { 
    this.data = <EditUserData> this.router.getCurrentNavigation().extras.state;
    this.statusIsActive = this.data.isActivated;
    this.statusIsRegist = this.data.isRegistered;
    this.statusBoolean = (this.statusIsActive === '1');
  }

  ngOnInit(): void {
    this.setupQuestions();
    this.setupReadonlyQuestions();
    this.initiateForm();
    
    const controls = [...this.formObj.params, ...this.formObj.components];
    this.msxForm = this.fcs.toFormGroup(controls);
  }

  async getIdAddress(){
    await this.ipService.getIpAddress().toPromise().then(ipResponse =>{
      this.ipAddress = ipResponse.ip;
    })
  }

  setupReadonlyQuestions(){
    this.questions = {
      loginId: new QuestionTextbox({
        key: 'loginId',
        label: 'Email',
        readonly: true
      }),
      vendorName: new QuestionTextbox({
        key: 'vendorName',
        label: 'Vendor',
        readonly: true
      }),
      fullName: new QuestionTextbox({
        key: 'fullName',
        label: 'Name',
        readonly: true
      }),
      noKtp: new QuestionTextbox({
        key: 'noKtp',
        label: 'No KTP',
        readonly: true
      }),
      dateOfBirth: new QuestionTextbox({
        key: 'dateOfBirth',
        label: 'Date of Birth',
        width: 140,
        readonly: true
      })
    }
  }

  setupQuestions() {
    this.formObj = {
      name: 'editUserData',
      mode: CommonConstant.MODE_EDIT,
      colSize: 12,
      direction: FormConstant.DIRECTION_HORIZONTAL,
      components: [
        new QuestionTextbox({
          key: 'loginId',
          label: 'Email',
          readonly: true
        }),
        new QuestionTextbox({
          key: 'vendorName',
          label: 'Vendor',
          readonly: true
        }),
        new QuestionTextbox({
          key: 'fullName',
          label: 'Name',
          readonly: true
        }),
        new QuestionTextbox({
          key: 'gender',
          label: 'Gender',
          readonly: true
        }),
        new QuestionTextbox({
          key: 'kelurahan',
          label: 'Kelurahan',
          readonly: true
        }),
        new QuestionTextbox({
          key: 'kecamatan',
          label: 'Kecamatan',
          readonly: true
        }),
        new QuestionTextbox({
          key: 'city',
          label: 'City',
          readonly: true
        }),
        new QuestionTextbox({
          key: 'zipCode',
          label: 'Postal Code',
          readonly: true
        }),
        new QuestionTextbox({
          key: 'dateOfBirth',
          label: 'Date of Birth',
          width: 140,
          readonly: true
        }),
        new QuestionTextbox({
          key: 'placeOfBirth',
          label: 'Place of Birth',
          readonly: true
        }),
        new QuestionTextbox({
          key: 'province',
          label: 'Province',
          readonly: true
        }),
        new QuestionTextbox({
          key: 'noKtp',
          label: 'No KTP',
          readonly: true
        }),
        new QuestionTextbox({
          key: 'noPhone',
          label: 'No Handphone',
          placeholder: 'Phone number'
        }),
        new QuestionTextbox({
          key: 'newEmail',
          label: 'Email',
          placeholder: 'Please type email here'
        }),
        new QuestionTextarea({
          key: 'address',
          label: 'Address',
          placeholder: 'Address',
          readonly: true
        }),
        new QuestionTextbox({
          key: 'isRegist',
          label: 'Registration Status',
          value: this.data?.isRegistered === '1'
        }),
        new QuestionTextbox({
          key: 'isActive',
          label: 'Activation Status',
          value: this.data?.isActivated === '1'
        })
      ],
      params: []
    }
  }

  getQuestion(key: string) {
    return this.formObj.components.find(x => x.key === key);
  }

  onCancel() {
    this.location.back();
  }

  initiateForm(){
    this.userForm = this.formBuilder.group({
      loginId: [this.data?.loginId],
      vendorName: [this.data?.vendorName],
      fullName: [this.data?.fullName],
      noKtp: [this.data?.noKtp],
      dateOfBirth: [this.data?.dateOfBirth]
    })

  }
  
  onSubmit(){
    this.formData = this.msxForm.getRawValue();
    const request: EditUserDataRequest = new EditUserDataRequest();
    request.loginId = this.data.loginId;
    request.vendorCode = this.data.vendorCode;
    // request.fullName = this.formData.fullName;
    // request.gender = this.formData.gender;
    // request.kelurahan = this.formData.kelurahan;
    // request.kecamatan = this.formData.kecamatan;
    // request.city = this.formData.city;
    // request.zipCode = this.formData.zipCode;
    // request.dateOfBirth = this.parseDate(this.formData.dateOfBirth);
    // request.placeOfBirth = this.formData.placeOfBirth;
    // request.province = this.formData.province;
    // request.noKtp = this.formData.noKtp;
    request.noPhone = this.formData.noPhone;
    request.newEmail = this.formData.newEmail;
    // request.address = this.formData.address;
    // request.isActive = this.formData.isActive ? '1' : '0';
    // request.isRegist = this.formData.isRegist ? '1' : '0';
    request.ipAddress = this.ipAddress;

    this.userService.updateDataSigner(request).subscribe(res => {
      if (res.status.code === 0) {
        return Success(res.status.message).then(() => {
          this.doBack();
        });
      } else {
        return Error(res.status.message);
      }
    });
  }

  parseDate(strDate: any) {
    const rawDate = `${strDate.year}-${strDate.month}-${strDate.day}`;
    if (!strDate) {
      return '';
    }
    const date = moment(rawDate).format('YYYY-MM-DD');
    return date;
  }

  doBack() {
    this.router.navigate([PathConstant.UPDATE_USER]);
  }

}
