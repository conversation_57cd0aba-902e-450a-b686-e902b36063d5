import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { DataRequest } from 'app/model/api/data.request';
import { VendorListRequest } from 'app/model/api/vendor-list.request';
import { TopupBalanceList } from 'app/model/topup-balance-list';
import { BalanceService } from 'app/services/api/balance.service';
import { DataService } from 'app/services/api/data.service';
import { FormConstant } from 'app/shared/components/ms-form/constants/form.constant';
import { FormModel } from 'app/shared/components/ms-form/models';
import { QuestionDate, QuestionDropdown } from 'app/shared/components/ms-form/questions';
import { Act } from 'app/shared/components/msx-datatable/enums/act';
import { ColumnType } from 'app/shared/components/msx-datatable/enums/column-type';
import { Table } from 'app/shared/components/msx-datatable/models/table';
import { Button } from 'app/shared/components/msx-view/models/Button';
import { MsxView } from 'app/shared/components/msx-view/models/MsxView';
import { WidgetType } from 'app/shared/components/msx-view/models/WidgetType';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { GlobalService } from 'app/shared/data/global.service';
import { ExtendTopupBalanceComponent } from './extend-topup-balance/extend-topup-balance.component';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { EditTopupBalanceListComponent } from './edit-topup-balance-list/edit-topup-balance-list.component';

@Component({
  selector: 'app-topup-balance-list',
  templateUrl: './topup-balance-list.component.html',
  styleUrls: ['./topup-balance-list.component.scss']
})
export class TopupBalanceListComponent implements OnInit {
  serviceGetListTopupBalance = URLConstant.GetListTopupBalance;
  view: MsxView;

  searchFormObj: FormModel<any> = new FormModel<any>();
  form: FormGroup;

  constructor(private global: GlobalService, private cdr: ChangeDetectorRef, private modalService: NgbModal, private router: Router,
    private dataService: DataService, private balanceService: BalanceService) { }

  async ngOnInit() {
    this.initView();
  }

  initView() {
    this.searchFormObj = {
      name: 'SearchForm',
      colSize: 6,
      autoload: true,
      direction: FormConstant.DIRECTION_HORIZONTAL,
      components: [
        new QuestionDropdown({
          key: 'tenantCode',
          label: 'Tenant Name',
          placeholder: 'Select Tenant',
          serviceUrl: URLConstant.GetTenantList,
          params: {
            status: '1'
          },
          options: [
            { key: '', value: 'All' }
          ],
          args: {
            list: 'tenantList',
            key: 'tenantCode',
            value: 'tenantName'
          }
        }),
        new QuestionDropdown({
          key: 'vendorCode',
          label: 'Vendor Name',
          placeholder: CommonConstant.CONST_SELECT_VENDOR,
          serviceUrl: URLConstant.GetVendorListV2,
          params: {
            status: '1'
          },
          options: [
            { key: '', value: 'All' }
          ],
          args: {
            list: 'vendorList',
            key: 'code',
            value: 'name'
          }
        }),
        new QuestionDropdown({
          key: 'balanceTypeCode',
          label: 'Balance Type',
          placeholder: 'Select Balance Type',
          serviceUrl: URLConstant.GetLov,
          options: [
            { key: '', value: 'All' }
          ],
          value: '',
          args: {
            list: 'lovList',
            key: 'code',
            value: 'description'
          },
          params: {
            lovGroup: 'BALANCE_TYPE'
          }
        }),
        new QuestionDropdown({
          key: 'status',
          label: 'Status',
          placeholder: 'Select Status',
          options: [
            { key: '', value: 'All' },
            { key: 'Is Used', value: 'Is Used' },
            { key: 'Not Used', value: 'Not Used' }
          ],
          value: ''
        }),
        new QuestionDate({
          key: 'expiredDateStart',
          label: 'Expired Date Start',
          placeholder: CommonConstant.FORMAT_DATE
        }),
        new QuestionDate({
          key: 'expiredDateEnd',
          label: 'Expired Date End',
          placeholder: CommonConstant.FORMAT_DATE
        }),
        {
          key: 'refNumber',
          label: 'Invoice No',
          placeholder: 'Type invoice number here',
          controlType: FormConstant.TYPE_TEXT
        },
        {
          key: 'description',
          label: 'Description',
          placeholder: 'Type description here',
          controlType: FormConstant.TYPE_TEXT
        },
      ],
      params: [
        {
          key: 'audit',
          controlType: FormConstant.TYPE_HIDDEN,
          value: {
            callerId: this.global.user.loginId
          }
        },
        {
          key: 'page',
          controlType: FormConstant.TYPE_HIDDEN,
          value: 1
        }
      ]
    }

    const TopupBalanceTable: Table<TopupBalanceList> = {
      name: 'topupBalance',
      list: [],
      columns: [
        {
          type: ColumnType.Number,
          prop: 'no',
          label: 'No.',
          width: 30
        },
        {
          type: ColumnType.Text,
          prop: 'tenantName',
          label: 'Tenant',
          width: 60
        },
        {
          type: ColumnType.Text,
          prop: 'vendorName',
          label: 'Vendor',
          width: 60
        },
        {
          type: ColumnType.Text,
          prop: 'balanceName',
          label: 'Balance',
          width: 30
        },
        {
          type: ColumnType.Date,
          format: 'DD-MMM-YYYY',
          prop: 'topupDate',
          label: 'Topup Date',
          width: 80
        },
        {
          type: ColumnType.Date,
          format: 'DD-MMM-YYYY',
          prop: 'expiredDate',
          label: 'Expired Date',
          width: 80
        },
        {
          type: ColumnType.Date,
          format: 'DD-MMM-YYYY',
          prop: 'lastExpiredDate',
          label: 'Last Expired Date',
          width: 80
        },
        {
          type: ColumnType.Number,
          prop: 'extendExpiredAttempt',
          label: 'Extend Expired Attempt',
          width: 60
        },
        {
          type: ColumnType.Number,
          prop: 'balancePrice',
          label: 'Balance Price',
          width: 40
        },
        {
          type: ColumnType.Text,
          prop: 'refNumber',
          label: 'Invoice No',
          width: 60
        },
        {
          type: ColumnType.Action,
          label: 'Action',
          width: 40,
          action: [
            {
              class: CommonConstant.TEXT_PRIMARY,
              icon: 'ft-edit',
              descr: 'Edit Ref Number',
              type: Act.Edit
            },
            {
              class: CommonConstant.TEXT_PRIMARY,
              icon: 'ft-settings',
              type: Act.Setting,
              descr: 'Extend Top Up Balance'
            }
          ]
        }
      ]
    }

    this.view = {
      title: 'Topup Balance List',
      components: [
        {
          type: WidgetType.SearchFilter,
          component: this.searchFormObj
        },
        {
          type: WidgetType.Datatable,
          component: TopupBalanceTable
        }
      ]
    }
  }

  onForm(form: FormGroup) {
    this.form = form;
  }

  openEditRefNumber(data) {
    const modal = this.modalService.open(EditTopupBalanceListComponent, { size: 'lg' });

    modal.componentInstance.tenantCode = data.tenantCode;
    modal.componentInstance.vendorCode = data.vendorCode;
    modal.componentInstance.tenantName = data.tenantName;
    modal.componentInstance.vendorName = data.vendorName;
    modal.componentInstance.balanceTypeCode = data.balanceName;
    modal.componentInstance.topupDate = data.topupDate;
    modal.componentInstance.refNo = data.refNumber;
    modal.componentInstance.idBalanceMutation = data.idBalanceMutation;
  }

  onItemClickListener(event: any) {
    const data = event['data'];
    console.log('On click', data);

    switch (event['act']['type']) {
      case Act.Edit:
        return this.openEditRefNumber(data);
      case Act.Setting:
        this.router.navigate([PathConstant.EXTEND_BALANCE_TOPUP],{state: data});
    }
  }
}
