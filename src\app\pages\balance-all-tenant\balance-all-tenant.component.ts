import { HttpClient } from '@angular/common/http';
import { ChangeDetectorRef, Component, NgZone, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { BalanceRequest } from 'app/model/api/balance.request';
import { VendorListRequest } from 'app/model/api/vendor-list.request';
import { AuditContext } from 'app/model/audit.context';
import { BalanceMutation } from 'app/model/balance-mutation';
import { FormConstant } from 'app/shared/components/ms-form/constants/form.constant';
import { FormModel } from 'app/shared/components/ms-form/models';
import { QuestionDate, QuestionDropdown } from 'app/shared/components/ms-form/questions';
import { ColumnType } from 'app/shared/components/msx-datatable/enums/column-type';
import { Table } from 'app/shared/components/msx-datatable/models/table';
import { MsxView } from 'app/shared/components/msx-view/models/MsxView';
import { WidgetType } from 'app/shared/components/msx-view/models/WidgetType';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { GlobalService } from 'app/shared/data/global.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { saveAs } from 'file-saver';
import { ColumnMode } from '@swimlane/ngx-datatable';
import * as swalFunction from '../../shared/data/sweet-alerts';
import { Tenant } from 'app/model/tenant';
import { BalanceService } from 'app/services/api/balance.service';
import { BalanceMutationFileRequest } from 'app/model/api/balance.mutation.file.request';

@Component({
  selector: 'app-balance-all-tenant',
  templateUrl: './balance-all-tenant.component.html',
  styleUrls: ['./balance-all-tenant.component.scss']
})
export class BalanceAllTenantComponent implements OnInit {
  public ColumnMode = ColumnMode;
  public tempData = [];
  serviceBalanceMutation = URLConstant.GetListBalanceMutationAllTenant
  view: MsxView;
  swal = swalFunction;
  isVisible = false;
  isVendorDDLVisible = false;
  isVendorDDLDisable = false;
  balances = [];
  waiting = false;

  vendors: any[];
  tenants: any[];
  tenantCode: string;
  childKey = 1;
  balanceType: string;
  refNumber: string;
  transactionType: string;
  documentName: string;
  transactionDateStart: string;
  transactionDateEnd: string;
  documentType: string;
  officeCode: string;
  status: string;

  vendorForm = this.formBuilder.group({
    vendorSelect: ['']
  });

  tenantForm = this.formBuilder.group({
    tenantSelect: ['']
  });

  searchFormObj: FormModel<any> = new FormModel<any>();
  form: FormGroup;

  constructor(private formBuilder: FormBuilder, private cdr: ChangeDetectorRef, private global: GlobalService, private http: HttpClient,
    private toastrService: ToastrService, private ngZone: NgZone, private cdk: ChangeDetectorRef,
    private spinner: NgxSpinnerService, private balanceService: BalanceService) {

  };

  async ngOnInit() {
    this.getTenant();
  };

  private dateToString(dateObject: any) {
    return (dateObject['year']) ? dateObject['year'] + '-' + dateObject['month'] + '-' + dateObject['day'] : dateObject;
  }

  resetSearchFilter() {
    this.balanceType = '';
    this.transactionType = '';
  }

  setLoading(){
    this.waiting = true;
    this.balances = [];
  }

  initView() {
    this.reloadChild();
    this.searchFormObj = {
      name: 'SearchForm',
      colSize: 6,
      exportExcel: true,
      direction: FormConstant.DIRECTION_HORIZONTAL,
      components: [
        new QuestionDropdown({
          key: 'balanceType',
          label: 'Balance Type',
          placeholder: 'Select Balance Type',
          serviceUrl: URLConstant.GetBalanceType,
          options: [
            { key: '', value: 'All' }
          ],
          value: this.balanceType,
          args: {
            list: 'listBalanceType',
            key: 'balanceTypeCode',
            value: 'balanceTypeName'
          },
          required: true,
          validations: [
            { type: 'required', message: 'Payment Sign Type harus di isi.' }
          ],
          params: {
            lovGroup: 'BALANCE_TYPE',
            tenantCode: this.tenantCode,
            vendorCode: this.global.vendor
          }
        }),
        {
          key: 'referenceNo',
          label: 'Reference No',
          value: !this.refNumber ? '' : this.refNumber,
          placeholder: 'Type reference number here',
          controlType: FormConstant.TYPE_TEXT
        },
        new QuestionDropdown({
          key: 'transactionType',
          label: 'Transaction Type',
          placeholder: 'Select Transaction Type',
          serviceUrl: URLConstant.GetLov,
          options: [
            { key: '', value: 'All' }
          ],
          value: '',
          args: {
            list: 'lovList',
            key: 'code',
            value: 'description'
          },
          params: {
            lovGroup: 'TRX_TYPE',
            constraint1: this.balanceType
          }
        }),
        {
          key: 'documentName',
          label: 'Document Name',
          value: !this.documentName ? '' : this.documentName,
          placeholder: 'Type document name here',
          controlType: FormConstant.TYPE_TEXT
        },
        new QuestionDate({
          key: 'transactionDateStart',
          value: !this.transactionDateStart ? '' : this.transactionDateStart,
          label: 'Transaction Date From',
          placeholder: CommonConstant.FORMAT_DATE
        }),
        {
          key: 'transactionDateEnd',
          value: !this.transactionDateEnd ? '' : this.transactionDateEnd,
          label: 'Transaction Date To',
          placeholder: CommonConstant.FORMAT_DATE,
          controlType: FormConstant.TYPE_DATE
        },
        new QuestionDropdown({
          key: 'documentType',
          value: !this.documentType ? '' : this.documentType,
          label: 'Document Type',
          placeholder: 'Select Document Type',
          serviceUrl: URLConstant.GetLov,
          options: [
            { key: '', value: 'All' }
          ],
          args: {
            list: 'lovList',
            key: 'code',
            value: 'description'
          },
          params: {
            lovGroup: 'DOC_TYPE'
          }
        }),
        new QuestionDropdown({
          key: 'officeCode',
          label: 'Office',
          value: !this.officeCode ? '' : this.officeCode,
          placeholder: 'Select Office',
          serviceUrl: URLConstant.OfficeList,
          options: [
            { key: '', value: 'All' }
          ],
          args: {
            list: 'officeList',
            key: 'officeCode',
            value: 'officeName'
          },
          params: {
            tenantCode: this.tenantCode
          }
        }),

        new QuestionDropdown({
          key: 'status',
          label: 'Status',
          value: !this.status ? '' : this.status,
          placeholder: 'Select Status',
          options: [
            { key: '', value: 'All' },
            { key: 'Success', value: 'Success' },
            { key: 'Failed', value: 'Failed' }
          ],
          args: {
          },
          params: {
          }
        })
      ],
      params: [
        {
          key: 'tenantCode',
          controlType: FormConstant.TYPE_HIDDEN,
          value: this.tenantCode
        },
        {
          key: 'vendorCode',
          controlType: FormConstant.TYPE_HIDDEN,
          value: this.global.vendor
        },
        {
          key: 'audit',
          controlType: FormConstant.TYPE_HIDDEN,
          value: {
            callerId: this.global.user.loginId
          }
        },
        {
          key: 'page',
          controlType: FormConstant.TYPE_HIDDEN,
          value: 1
        }
      ]
    }

    const BalanceMutationTable: Table<BalanceMutation> = {
      name: 'listMutation',
      list: [],
      columns: [
        {
          type: ColumnType.Text,
          prop: 'transactionNo',
          label: 'Trx No',
          width: 50
        },
        {
          type: ColumnType.Text,
          prop: 'transactionDate',
          label: 'Trx Date',
          width: 120
        },
        {
          type: ColumnType.Text,
          prop: 'officeName',
          label: 'Office',
          width: 80
        },
        {
          type: ColumnType.Text,
          prop: 'transactionType',
          label: 'Trx Type',
          width: 100
        },
        {
          type: ColumnType.Text,
          prop: 'customerName',
          label: 'Trx By',
          width: 80
        },
        {
          type: ColumnType.Text,
          prop: 'refNumber',
          label: 'Ref No',
          width: 150
        },
        {
          type: ColumnType.Text,
          prop: 'documentType',
          label: 'Doc Type',
          width: 110
        },
        {
          type: ColumnType.Text,
          prop: 'documentName',
          label: 'Doc Name',
          width: 120
        },
        {
          type: ColumnType.Text,
          prop: 'notes',
          label: 'Notes',
          width: 150
        },
        {
          type: ColumnType.Currency,
          prop: 'qty',
          label: 'Qty',
          width: 70
        },
        {
          type: ColumnType.Currency,
          prop: 'balance',
          label: 'Balance',
          width: 70
        }
      ]
    };
    this.vendors[0].code == '' ? 
    this.view = {
      title: '',
      components: [
      ]
    }
    :
    this.view = {
      title: 'Balance Mutation',
      components: [
        {
          type: WidgetType.SearchFilter,
          component: this.searchFormObj
        },
        {
          type: WidgetType.Datatable,
          component: BalanceMutationTable
        }
      ]
    }

  }

  changeVendor(e) {
    this.setLoading();
    this.global.vendor = e.target.value;
    this.getBalance(this.tenantCode, this.global.vendor);
    this.resetSearchFilter();
    this.initView();
  }

  changeTenant(e) {
    this.setLoading();
    this.tenantCode = e.target.value;
    this.isVendorDDLVisible = false;
    this.getVendor();
    this.spinner.hide();
    this.waiting = false;
  }

  onFormListener(form: FormGroup) {
    this.form = form;
  }

  onDropDownSelect(event: { prop: string; data: { key: string; value: string } }) {
    if (event.prop === 'balanceType') {
      this.balanceType = event.data.key;
      this.initView();
    }
    this.spinner.hide();
  }
  removeCircularReferences() {
    const seen = new WeakSet();
    return (key: any, value: any) => {
      if (typeof value === "object" && value !== null) {
        if (seen.has(value)) {
          return; // Ignore circular reference
        }
        seen.add(value);
      }
      return value;
    };
  }

  reloadChild() {
    if (this.isVisible) {
      this.childKey = 0; // Remove child
    }
    
    setTimeout(() => {
      this.childKey = Math.random(); // Assign new key (forces re-creation)
    }, 1);
  }

  getTenant() {
    const tenantListReq: Tenant = new Tenant();
    tenantListReq.tenantCode = this.global.user.role.tenantCode;
    tenantListReq.status = '1';
    this.http.post(URLConstant.GetTenantList, tenantListReq).subscribe((response) => {
      if (response['status']['code'] !== 0) {
        this.toastrService.error(response['status']['message']);
      } else {        
        this.tenants = [
          { tenantName: 'Select One', tenantCode: '', isActive: '' },
          ...response['tenantList']
        ];
        this.tenantCode = this.tenants[0].tenantCode;
        this.getVendor();
        this.tenantForm.patchValue({
          tenantSelect: this.tenantCode
        });
      }
    });
  }

  getVendor() {
    this.resetSearchFilter();
    const vendorListReq: VendorListRequest = new VendorListRequest();
    vendorListReq.audit = new AuditContext();
    vendorListReq.audit.callerId = this.global.user.loginId;
    vendorListReq.tenantCode = this.tenantCode;
    this.http.post(URLConstant.GetVendorList, vendorListReq).subscribe((response) => {
      if (response['status']['code'] !== 0) {
        this.toastrService.error(response['status']['message']);
      } else {
        this.vendors = response['vendorList'];
        this.global.vendor = this.vendors.length > 0 ? this.vendors[0].code : '';        
        if(this.vendors.length > 0){    
          this.isVisible = true;
          this.isVendorDDLDisable = false;
          this.getBalance(this.tenantCode, this.global.vendor);          
        }else{
          this.vendors = [
            { name: 'No Vendor', code: ''}
          ];
          this.isVendorDDLVisible = true;
          this.isVendorDDLDisable = true;
        }
        this.vendorForm.patchValue({
          vendorSelect: this.global.vendor
        });        
        this.initView();
      }
    });
  }

  async getBalance(tenantCode: string, vendorCode: string) {
    const balanceReq: BalanceRequest = new BalanceRequest();
    balanceReq.audit.callerId = this.global.user.loginId;
    balanceReq.tenantCode = tenantCode;
    balanceReq.vendorCode = vendorCode;    
    if(vendorCode && vendorCode.trim() !== ''){
      this.http.post(URLConstant.GetBalanceByTenantCodeAndVendorCodeAllTenant, balanceReq).subscribe(
        (response) => {
          if (response["status"]["code"] != 0) {
            this.toastrService.error(response["status"]["message"]);
            this.balances = [];
          } else {
            this.isVendorDDLVisible = true;
            this.balances = response["listBalance"];
            this.formatNumber(this.balances);
            this.waiting = false;
          }
        }
      );
    } else {
      this.isVendorDDLVisible = false;
    }
  }

  formatNumber(listBalance) {
    for (var i = 0; i < listBalance.length; i++) {
      listBalance[i].currentBalance = listBalance[i].currentBalance.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }
  }

  async getBalanceMutationFile(params) {
    const bmReq: BalanceMutationFileRequest = new BalanceMutationFileRequest();
    bmReq.tenantCode = this.tenantCode;
    bmReq.vendorCode = this.global.vendor;
    bmReq.balanceType = params.balanceType;
    bmReq.transactionType = params.transactionType;
    bmReq.documentType = params.documentType;
    bmReq.referenceNo = params.referenceNo;    
    bmReq.officeCode = params.officeCode;
    bmReq.documentName = params.documentName;
    bmReq.status = params.status;

    if (params.transactionDateStart != null && params.transactionDateStart != "") {
      bmReq.transactionDateStart = this.dateToString(params.transactionDateStart);
    }

    if (params.transactionDateEnd != null && params.transactionDateEnd != "") {
      bmReq.transactionDateEnd = this.dateToString(params.transactionDateEnd);
    }

    bmReq.audit = new AuditContext();
    bmReq.audit.callerId = this.global.user.loginId;
    await this.http.post(URLConstant.GetListBalanceMutationAllTenantFile, bmReq).toPromise().then(
      (response) => {
        if (response["status"]["code"] != 0) {
          // agar tidak muncul 2 kali message errornya
          //this.toastrService.error(response["status"]["message"]);
        } else {
          const blob = this.b64toBlob(response["excelBase64"]);
          saveAs(blob, response["filename"]);
        }
      }
    );
  }

  b64toBlob(b64Data, contentType = '', sliceSize = 512) {
    const byteCharacters = atob(b64Data);
    const byteArrays = [];

    for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
      const slice = byteCharacters.slice(offset, offset + sliceSize);

      const byteNumbers = new Array(slice.length);
      for (let i = 0; i < slice.length; i++) {
        byteNumbers[i] = slice.charCodeAt(i);
      }

      const byteArray = new Uint8Array(byteNumbers);
      byteArrays.push(byteArray);
    }

    const blob = new Blob(byteArrays, { type: contentType });
    return blob;
  }
}