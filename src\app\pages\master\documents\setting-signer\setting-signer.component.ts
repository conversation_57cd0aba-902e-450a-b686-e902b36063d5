import { Component, OnInit } from '@angular/core';
import {FormBuilder} from '@angular/forms';
import {ActivatedRoute, NavigationExtras, Router} from '@angular/router';
import {Location} from '@angular/common';
import {ColumnMode} from '@swimlane/ngx-datatable';
import {NgbModal} from '@ng-bootstrap/ng-bootstrap';
import {BoxAnnotation} from '../../../../model/box-annotation';
import {ToastrService} from 'ngx-toastr';
import {PathConstant} from '../../../../shared/constant/PathConstant';
import {AddDocumentTemplateRequest} from '../../../../shared/dto/document-template/add-document-template.request';
import {Signer} from '../../../../shared/components/document-anotate/model/signer';
import {HttpClient} from '@angular/common/http';
import {URLConstant} from '../../../../shared/constant/URLConstant';
import {FormConstant} from '../../../../shared/components/ms-form/constants/form.constant';
import {FormModel} from '../../../../shared/components/ms-form/models';
import {CommonConstant} from '../../../../shared/constant/common.constant';
import {SignerType} from '../../../../shared/data/signer-type';
import { GlobalService } from 'app/shared/data/global.service';
import {CreateStampRequest} from '../../../../model/api/create.stamp.request';
import {StampAnnotation} from '../../../../model/stamp-annotation';
import * as moment from 'moment';
import {ManualSignerRequest} from '../../../../model/api/manual.signer.request';
import {BaseResponse} from '../../../../model/api/base.response';
import { Success } from 'app/shared/data/sweet-alerts';
import { SaveManualStampRequest } from 'app/model/api/save.manual.stamp.request';

@Component({
  selector: 'app-setting-signer',
  templateUrl: './setting-signer.component.html',
  styleUrls: ['./setting-signer.component.scss']
})
export class SettingSignerComponent implements OnInit {

  public ColumnMode = ColumnMode;
  public state: any;
  public numOfPage: number;
  public mode: string;
  public annotations: BoxAnnotation[];
  public signer: Signer[];
  public users: Signer[];
  public sdtOnly: boolean;
  mForm: FormModel<any>;
  msg: string;
  public sequence: any;

  constructor(private formBuilder: FormBuilder, private router: Router, private location: Location,
              private modalService: NgbModal, private toastrService: ToastrService, private http: HttpClient,
              private activeRoute: ActivatedRoute, private global: GlobalService) {
    this.activeRoute.queryParams.subscribe(params => {
      this.mode  = params['mode'];
      this.state = this.router.getCurrentNavigation().extras.state;
      this.sdtOnly = !!this.state['sdt'];
      localStorage.setItem('sdtOnly', this.sdtOnly ? '1' : '0');
      if (this.mode === CommonConstant.MODE_SIGNER || this.sdtOnly === true) {
        this.signer = this.state['signer'];
      }

      if (this.mode === CommonConstant.MODE_MANUAL_SIGNER) {
        this.users = this.state?.users;
      }

      if (params['msg']) {
        this.msg = params['msg'];
      }
      this.sequence = this.state.isSequence;
    

    });
  }

  ngOnInit(): void {
    this.initForm();
    console.log('state', this.state);
  }

  doBack() {
    this.location.back();
  }

  onAnnotations(result: BoxAnnotation[]) {
    this.annotations = result;

    if (this.users) {
      const ttds = this.annotations.filter(x => x.type === SignerType.TTD);

      // Reset sign locations (to avoid lingering deleted box)
      for (const ttd of ttds) {
        const user = this.users.find(usr => usr.phone === ttd.phone);
        user.signLocations = [];
      } 

      // Fill all sign locations 
      for (const ttd of ttds) {
        const user = this.users.find(usr => usr.phone === ttd.phone);

        let ttdObj: {
          id?: any;
          signPage: any;
          transform?: string;
          position?: string;
          positionVida: string;
          positionPrivy: string;
          signLocation: {
            llx: number;
            lly: number;
            urx: number;
            ury: number;
          }
        };

        if (!user.signLocations) {
          ttdObj = {
            id: ttd.id,
            signPage: ttd.page,
            transform: ttd.transform,
            position: ttd.coordinate,
            positionVida: ttd.coordinateVida,
            positionPrivy: ttd.coordinatePrivy,
            signLocation: {
              llx: ttd.lx,
              lly: ttd.ly,
              urx: ttd.rx,
              ury: ttd.ry
            }};
          user.signLocations = [ttdObj];
        } else {
          const tmp = user.signLocations.find(x => x.id === ttd.id);

          if (tmp) {
            tmp.signPage = ttd.page;
            tmp.transform = ttd.transform;
            tmp.position  = ttd.coordinate;
            tmp.positionVida = ttd.coordinateVida,
            tmp.positionPrivy = ttd.coordinatePrivy,
            tmp.signLocation = {
              llx: ttd.lx,
              urx: ttd.rx,
              lly: ttd.ly,
              ury: ttd.ry
            };
          } else {
            ttdObj = {
              id: ttd.id,
              signPage: ttd.page,
              transform: ttd.transform,
              position: ttd.coordinate,
              positionVida: ttd.coordinateVida,
              positionPrivy: ttd.coordinatePrivy,
              signLocation: {
                llx: ttd.lx,
                lly: ttd.ly,
                urx: ttd.rx,
                ury: ttd.ry
              }};

            user.signLocations.push(ttdObj);
          }
        }
      }

      console.log('users signer', this.users);
    }

    console.log('annotations', result);
  }

  onNumOfPage(pageSize) {
    this.numOfPage = pageSize;
  }

  onSubmit() {
    if (!this.sdtOnly) {
      this.submitTemplate();
      return;
    }
    if (this.state['manualStamp']) {
      this.submitManualStamp();
    }else { 
      this.submitSdtOnly();}
  }

  submitManualStamp(){
    if (!this.annotations || this.annotations.length === 0) {
      this.toastrService.warning('Silahkan tambahkan 1 meterai terlebih dulu.', null, {
        positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
      });
      return;
    }

    const documentBase64 = this.state['rawTemplate'];
    const request: SaveManualStampRequest = new SaveManualStampRequest();
    request.refNo = this.state['documentNo'];
    request.docName = this.state['documentName'];
    request.docDate = this.parseDateValue(this.state['documentDate']);
    request.peruriDocType = this.state['documentTypePeruri'];
    request.docType = this.state['documentType'];
    request.docFile = documentBase64.replace(CommonConstant.DATA_APPLICATION_PDF_BASE64, '');

    const stamps: StampAnnotation[] = [];
    this.annotations.forEach(data => {
      stamps.push(StampAnnotation.fromSigner(this.validateSigner(new Signer(data))));
    });

    console.log('stamp annotations', stamps);
    request.stampingLocations = stamps;

    this.http.post(URLConstant.SaveManualStampRequest, request).subscribe(response => {
      if (response['status']['code'] === 0) {
        this.toastrService.success('Permintaan pembubuhan e-Meterai berhasil dibuat.', null, {
          positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
        });
      }
      this.router.navigate(['master', 'documents', 'manual-stamp'])
    });

  }

  submitSdtOnly() {
    if (!this.annotations || this.annotations.length === 0) {
      this.toastrService.warning('Silahkan tambahkan 1 meterai terlebih dulu.', null, {
        positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
      });
      return;
    }

    const documentBase64 = this.state['rawTemplate'];
    const request: CreateStampRequest = new CreateStampRequest();
    request.refNo = this.state['documentNo'];
    request.docName = this.state['documentName'];
    request.docDate = this.parseDateValue(this.state['documentDate']);
    request.peruriDocType = this.state['documentTypePeruri'];
    request.docType = this.state['documentType'];
    request.docTemplate = this.state['templateDocument'];
    request.docNominal = this.state['documentAmount'];
    request.docFile = documentBase64.replace(CommonConstant.DATA_APPLICATION_PDF_BASE64, '');
    request.idType = this.state['identityType'];
    request.idNo = this.state['identityNo'];
    request.owedsName = this.state['debtName'];
    request.msg = this.msg;

    const stamps: StampAnnotation[] = [];
    this.annotations.forEach(data => {
      stamps.push(StampAnnotation.fromSigner(this.validateSigner(new Signer(data))));
    });

    console.log('stamp annotations', stamps);
    request.stampingLocations = stamps;

    this.http.post(URLConstant.InsertStampingEmbed, request).subscribe(response => {
      if (response['status']['code'] !== 0) {
        console.log('Error', response);
        return;
      }

      this.router.navigate([PathConstant.EMBED_MONITORING_E_MATERAI],
        {state: {msg: 'Document Template berhasil di simpan.'}, queryParams: {msg: this.msg}})
    });
  }

  submitTemplate() {
    if (!this.hasSignature(this.annotations)) {
      const msg = this.users ? 'Silahkan tambahkan minimal 1 tandatangan terlebih dulu.' : 'Silahkan tambahkan 1 tandatangan atau 1 paraf terlebih dulu.';
      this.toastrService.warning(msg, null, {
        positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
      });
      return;
    }

    if (this.mode === CommonConstant.MODE_MANUAL_SIGNER) {
      this.submitManualSigner();
      return;
    }

    const documentBase64 = this.state['rawTemplate'];
    const request: AddDocumentTemplateRequest = new AddDocumentTemplateRequest();
    request.documentExample = documentBase64.replace(CommonConstant.DATA_APPLICATION_PDF_BASE64, '');
    request.documentTemplateCode = this.state['documentTemplateCode'];
    request.documentTemplateName = this.state['documentTemplateName'];
    request.documentTemplateDescription = this.state['documentTemplateDescription'];
    request.isActive = this.state['isActive'];
    request.isSequence = this.state['isSequence'];
    request.numberOfPage = this.numOfPage;
    request.paymentSignTypeCode = this.state['paymentSignTypeCode'];
    request.useSignQr = this.state['useSignQr'];
    request.prioritySequence = this.state['prioritySequence'] ? Number(this.state['prioritySequence']) : 0;
    
    request.tenantCode = this.global.user.roles[0].tenantCode;
    request.vendorCode = this.state['vendorCode'];

    if (this.mode === CommonConstant.MODE_SIGNER) {
      request.isSignLocOnly = '1';
    } else {
      request.isSignLocOnly = '0';
    }

    request.signer = [];
    this.annotations.forEach(data => {
      request.signer.push(this.validateSigner(new Signer(data)));
    });
    
    if(request.isSequence === '1'){
      const extras: NavigationExtras = {
        state: {
          data: this.state,
          ttd: this.annotations,
          page: this.numOfPage,
          mode: this.mode
        }}
  
      this.router.navigate([PathConstant.SETTING_SEQUENTIAL_SIGNING], extras)
    } 
    else {
      console.log('Request Add/Update Document', request);
      const serviceUrl = this.mode === CommonConstant.MODE_ADD ? URLConstant.AddTemplate : URLConstant.UpdateTemplate;
      this.http.post(serviceUrl, request).subscribe(response => {
        if (response['status']['code'] !== 0) {
          console.log('Error', response);
          return;
        }
        return Success('Document Template berhasil di simpan').then(() => {
          this.router.navigate([PathConstant.LIST_DOCUMENT_TEMPLATE], {state: {msg: 'Document Template berhasil di simpan.'}})
        }); 
      });
    }
  }

  submitManualSigner() {
    // Check is user has already have signer
    let isValid = true;
    for (const user of this.users) {
      const signer = this.annotations.find(x => x.phone === user.phone);

      if (!signer) {
        isValid = false;
        this.toastrService.warning(`Silahkan tambahkan lokasi tandatangan untuk ${user.name}`, null, {
          positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
        });
        break;
      }
    }

    if (!isValid) {
      return;
    }

    // Check peruri doc type
    const sdt = this.annotations.find(x => x.type === SignerType.SDT);
    if (this.state?.documentTypePeruri !== '' && !sdt) {
      this.toastrService.warning(`Silahkan tambahkan minimal 1 meterai terlebih dulu!`, null, {
        positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
      });
      return;
    }

    const documentBase64 = this.state['rawTemplate'];
    const request: ManualSignerRequest = new ManualSignerRequest();

    request.psreCode      = this.state?.psreCode;
    request.referenceNo   = this.state?.referenceNo;
    request.documentName  = this.state?.documentName;
    request.documentDate  = this.parseDateValue(this.state?.documentDate);
    request.peruriDocType = this.state?.documentTypePeruri;
    request.isAutomaticStamp = this.state?.isAutomaticStamp;
    request.paymentType   = this.state?.paymentType;
    request.isSequence    = this.sequence;
    request.officeCode    = this.state?.officeCode;
    request.businessLineCode = this.state?.businessLineCode;
    request.regionCode    = this.state?.regionCode;
    request.useSignQr     = this.state?.useSignQr;

    const stamps: StampAnnotation[] = [];
    const listStamp = this.annotations.filter(x => x.type === SignerType.SDT);
    listStamp.forEach(data => {
      stamps.push(StampAnnotation.fromSigner(this.validateSigner(new Signer(data))));
    });

    request.stampingLocations = stamps;
    request.signers       = this.users;
    request.documentFile  = documentBase64.replace(CommonConstant.DATA_APPLICATION_PDF_BASE64, '');
    console.log('Manual Signer Request', request);

    if(request.isSequence === '1'){
      const extras: NavigationExtras = {
        state: {
          data: this.state,
          ttd: this.annotations,
          page: this.numOfPage,
          mode: this.mode,
          users : this.users
        }}  
  
      this.router.navigate([PathConstant.MANUAL_SEQUENTIAL], extras)
    } 
    else {
    this.http.post<BaseResponse>(URLConstant.ManualUploadSigner, request).subscribe(response => {
      if (response.status.code === 0) {
        this.toastrService.success('Permintaan tanda tangan berhasil dibuat.', null, {
          positionClass: CommonConstant.TOAST_BOTTOM_RIGHT
        });
        this.router.navigate(['master', 'documents', 'manual-upload']);
      }
    }) }
  }


  initForm() {
    if (this.sdtOnly) {
      this.mForm = {
        name: 'settingDocumentTemplate',
        colSize: 6,
        direction: FormConstant.DIRECTION_HORIZONTAL,
        components: [
          {
            key: 'documentNo',
            label: CommonConstant.LABEL_DOCUMENT_NUMBER,
            placeholder: CommonConstant.LABEL_DOCUMENT_NUMBER,
            controlType: FormConstant.TYPE_TEXT,
            value: this.state['documentNo'],
            readonly: true
          },
          {
            key: 'documentName',
            label: 'Document Name',
            placeholder: CommonConstant.DOCUMENT_TEMPLATE_NAME,
            controlType: FormConstant.TYPE_TEXT,
            value: this.state['documentName'],
            readonly: true
          },
          {
            key: 'documentTypePeruri',
            label: 'Document Type Peruri',
            placeholder: 'Document type peruri',
            controlType: FormConstant.TYPE_TEXT,
            value: this.getExtrasValue(this.state['documentTypePeruri']),
            readonly: true
          },
          {
            key: 'documentType',
            label: 'Document Type',
            placeholder: 'Document type',
            controlType: FormConstant.TYPE_TEXT,
            value: this.getExtrasValue(this.state['documentType']),
            readonly: true
          }
        ],
        params: []
      };
      return;
    }

    if (this.mode === CommonConstant.MODE_MANUAL_SIGNER) {
      this.mForm = {
        name: 'settingDocumentTemplate',
        colSize: 6,
        direction: FormConstant.DIRECTION_HORIZONTAL,
        components: [
          {
            key: 'documentNo',
            label: CommonConstant.LABEL_DOCUMENT_NUMBER,
            placeholder: CommonConstant.LABEL_DOCUMENT_NUMBER,
            controlType: FormConstant.TYPE_TEXT,
            value: this.state['referenceNo'],
            readonly: true
          },
          {
            key: 'documentName',
            label: 'Document Name',
            placeholder: CommonConstant.DOCUMENT_TEMPLATE_NAME,
            controlType: FormConstant.TYPE_TEXT,
            value: this.state['documentName'],
            readonly: true
          }
        ],
        params: []
      };
      return;
    }

    this.mForm = {
      name: 'settingDocumentTemplate',
      colSize: 6,
      direction: FormConstant.DIRECTION_HORIZONTAL,
      components: [
        {
          key: 'documentTemplateCode',
          label: 'Document Template Code',
          placeholder: 'Document template code',
          controlType: FormConstant.TYPE_TEXT,
          value: this.state['documentTemplateCode'],
          readonly: true
        },
        {
          key: 'documentTemplateName',
          label: 'Document Template Name',
          placeholder: CommonConstant.DOCUMENT_TEMPLATE_NAME,
          controlType: FormConstant.TYPE_TEXT,
          value: this.state['documentTemplateName'],
          readonly: true
        },
        {
          key: 'documentTemplateDescription',
          label: 'Description',
          placeholder: 'Description',
          controlType: FormConstant.TYPE_TEXT,
          value: this.state['documentTemplateDescription'],
          readonly: true
        },
        {
          key: 'isActive',
          label: 'Status',
          controlType: FormConstant.TYPE_TEXT,
          readonly: true,
          value: this.state['isActive'] === '1' ? 'Active' : 'Inactive'
        }
      ],
      params: []
    };
  }

  getExtrasValue(key: string) {
    return this.state['extras'].find(x => x['key'] === key)['value'];
  }

  hasSignature(annotations: BoxAnnotation[]) {
    if (!annotations) {
      return false;
    }

    if (annotations.length === 0) {
      return false;
    }

    const ttds = annotations.find(x => x.type === SignerType.TTD);
    const prfs = annotations.find(x => x.type === SignerType.PRF);
    return !!(ttds || prfs);
  }

  /**
   * Patch Lokasi ttd minus
   * @param mSginer
   */
  validateSigner(mSginer: Signer) {
    if (!this.signer) {
      return mSginer;
    }

    console.log('validate signer');
    if (mSginer.signTypeCode === 'SDT') {
      return this.validateSdt(mSginer);
    } else {
      const check = this.signer.find(x => x.signerTypeCode === mSginer.signerTypeCode &&
        x.signPage === mSginer.signPage);

      if (!check) {
        return mSginer;
      }

      if (mSginer.signLocation.llx < 0 && mSginer.signLocation.urx < 0) {
        return check;
      } else {
        return mSginer;
      }
    }
  }

  validateSdt(mSginer: Signer) {
    const sdt = this.signer.find(x => x.signTypeCode === mSginer.signTypeCode &&
      x.signPage === mSginer.signPage);

    if (!sdt) {
      return mSginer;
    }

    if (mSginer.signLocation.llx < 0 && mSginer.signLocation.urx < 0) {
      return sdt;
    } else {
      return mSginer;
    }
  }

  parseDateValue(dateObj) {
    const date  = new Date(dateObj['year'], dateObj['month'] - 1, dateObj['day']);
    return moment(date).format('YYYY-MM-DD');
  }
}
