<div class="row" style="margin-top: 15px">
    <div class="col-6">
      <div class="content-header" style="margin-top: 0 !important;" translate>Setting Sequential Signing</div>
    </div>
</div>

<div class="container">
<div class="col-12">
  <div class="row">
    <div cdkDropList class="example-list" (cdkDropListDropped)="drop($event)">
      <div class="example-box" *ngFor="let seqsign of seqsigns; let index = index" cdkDrag>{{index + 1 + '. '}}  {{seqsign}}</div>
    </div>
  </div>
<div class="tombol ">
<div class="row">
  <div class="btncancel">
    <div class="col-6 text-center">
        <button class="btn btn-light mr-2" (click)="onCancel()"  translate>Cancel</button>
    </div>
  </div>
  <div class="btnsave">
        <div class="col-6 text-center">
        <button class="btn btn-info" (click)="onSubmit()" translate>Next</button>
        </div>
  </div>
</div>
</div>
</div>
</div>


  
