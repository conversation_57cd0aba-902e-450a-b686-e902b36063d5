import { FormModel } from '../../../shared/components/ms-form/models';
import { FormConstant } from '../../../shared/components/ms-form/constants/form.constant';
import { QuestionDate, QuestionDropdown } from 'app/shared/components/ms-form/questions';
import { URLConstant } from 'app/shared/constant/URLConstant'; 
import { Document } from 'app/model/document';
import { Align } from 'app/shared/components/msx-datatable/enums/align';
import { CommonConstant } from 'app/shared/constant/common.constant';
  
import { Act } from "app/shared/components/msx-card-datatable/enums/act";
import { ColumnType } from "app/shared/components/msx-card-datatable/enums/column-type";
import { Table } from "app/shared/components/msx-card-datatable/models/table"; 


export const InquirySearchFilterCustomer: FormModel<string> = {
  name: 'inquirySearchForm',
  direction: FormConstant.DIRECTION_HORIZONTAL,
  colSize: 6,
  components: [
    new QuestionDropdown({
      key: 'transactionStatus',
      label: 'Status',
      placeholder: 'Select Status',
      serviceUrl: URLConstant.GetLov,
      options: [
        { key: '', value: 'All' }
      ],
      params: {
        lovGroup: 'LOV_SIGN_STATUS'
      },
      args: {
        list: 'lovList',
        key: 'code',
        value: 'description'
      },
      value: ''
    })
  ],
  params: []
};

export const InquiryTableCustomer: Table<Document> = {
  name: 'listDocument',
  list: [],
  columns: [
    {
      type: ColumnType.Text,
      prop: 'docTypeName',
      label: 'Doc Type',
      width: 140
    },
    {
      type: ColumnType.Text,
      prop: 'docTemplateName',
      label: 'Doc Name',
      width: 280
    },
    {
      type: ColumnType.Date,
      format: CommonConstant.FORMAT_DATE_WITHTIME,
      prop: 'requestDate',
      label: CommonConstant.LABEL_REQUEST_DATE,
      width: 140
    },
    {
      type: ColumnType.Date,
      format: CommonConstant.FORMAT_DATE_WITHTIME,
      prop: 'completeDate',
      label: CommonConstant.LABEL_COMPLETE_DATE,
      width: 140
    },
    {
      type: ColumnType.Text,
      prop: 'totalSigned',
      label: CommonConstant.LABEL_TOTAL_SIGNER_SIGNED,
      width: 100
    },
    {
      type: ColumnType.Text,
      prop: 'totalStamped',
      label: CommonConstant.LABEL_TOTAL_MATERAI,
      width: 100
    },
    {
      type: ColumnType.Text,
      prop: 'signStatus',
      label: CommonConstant.LABEL_STATUS_TTD,
      width: 90,
      class: CommonConstant.TEXT_DANGER,
      condition: true,
      conditionedClass: CommonConstant.TEXT_SUCCESS,
      conditionExpected: 'Complete'
    },
    {
      type: ColumnType.Action,
      label: 'Action',
      width: 140,
      action: [
        {
          class: CommonConstant.TEXT_PRIMARY,
          icon: 'ft-eye',
          type: Act.View,
          descr: 'View'
        },
        {
          class: CommonConstant.TEXT_PRIMARY,
          icon: CommonConstant.ICON_FT_DOWNLOAD,
          type: Act.Download,
          descr: 'Download'
        },
        {
          class: CommonConstant.TEXT_PRIMARY,
          icon: 'ft-user',
          type: Act.ViewSigner,
          descr: 'Signer'
        },
      ]
    }
  ]
};

export const InquiryTableCustomerMobile: Table<Document> = {
  name: 'listDocument',
  list: [],
  columns: [
    {
      type: ColumnType.Text,
      cardType: ColumnType.Detail,
      prop: 'docTypeName',
      label: 'Doc Type',
      width: 140
    },
    {
      type: ColumnType.Text,
      cardType: ColumnType.Title,
      prop: 'docTemplateName',
      label: 'Doc Name',
      width: 280
    },
    {
      type: ColumnType.Date,
      cardType: ColumnType.Detail,
      format: CommonConstant.FORMAT_DATE_WITHTIME,
      prop: 'requestDate',
      label: CommonConstant.LABEL_REQUEST_DATE,
      width: 140
    },
    {
      type: ColumnType.Date,
      cardType: ColumnType.Detail,
      format: CommonConstant.FORMAT_DATE_WITHTIME,
      prop: 'completeDate',
      label: CommonConstant.LABEL_COMPLETE_DATE,
      width: 140
    },
    {
      type: ColumnType.Text,
      cardType: ColumnType.Detail,
      prop: 'totalSigned',
      label: CommonConstant.LABEL_TOTAL_SIGNER_SIGNED,
      width: 100
    },
    {
      type: ColumnType.Text,
      cardType: ColumnType.Detail,
      prop: 'totalStamped',
      label: CommonConstant.LABEL_TOTAL_MATERAI,
      width: 100
    },
    {
      type: ColumnType.Text,
      prop: 'signStatus',
      label: CommonConstant.LABEL_STATUS_TTD,
      width: 90,
      class: CommonConstant.TEXT_DANGER,
      condition: true,
      conditionedClass: CommonConstant.TEXT_SUCCESS,
      conditionExpected: 'Complete',
      isStatusCardMobile: true,
    },
    {
      type: ColumnType.Action,
      label: 'Action',
      width: 140,
      action: [
        {
          class: CommonConstant.TEXT_PRIMARY,
          icon: 'ft-eye',
          type: Act.View,
          descr: 'View',
          cardAction: true,
          mobileMode: false,
        },
        {
          class: CommonConstant.TEXT_PRIMARY,
          icon: 'ft-user',
          type: Act.ViewSigner,
          descr: 'Signer',
          cardAction: false,
          mobileMode: false,
        },
      ]
    }
  ]
};

export const InquirySearchFilterBranchManager: FormModel<string> = {
  name: 'inquirySearchForm',
  direction: FormConstant.DIRECTION_HORIZONTAL,
  colSize: 6,
  components: [
    {
      key: 'customerName',
      label: CommonConstant.LABEL_CUSTOMER_NAME,
      placeholder: 'Type customer name here',
      controlType: FormConstant.TYPE_TEXT,
      value: ''
    },
    {
      key: 'refNumber',
      label: 'Reference Number',
      placeholder: 'Type reference number here',
      controlType: FormConstant.TYPE_TEXT,
      value: ''
    },
    new QuestionDate({
      key: 'requestedDateStart',
      label: 'Request Date From',
      placeholder: CommonConstant.FORMAT_DATE
    }),
    new QuestionDate({
      key: 'requestedDateEnd',
      label: 'Request Date To',
      placeholder: CommonConstant.FORMAT_DATE
    }),
    new QuestionDate({
      key: 'completedDateStart',
      label: 'Completed Date From',
      placeholder: CommonConstant.FORMAT_DATE
    }),
    new QuestionDate({
      key: 'completedDateEnd',
      label: 'Completed Date To',
      placeholder: CommonConstant.FORMAT_DATE
    }),
    new QuestionDropdown({
      key: 'docType',
      label: 'Document Type',
      placeholder: 'Select Document Type',
      serviceUrl: URLConstant.GetLov,
      options: [
        { key: '', value: 'All' }
      ],
      args: {
        list: 'lovList',
        key: 'code',
        value: 'description'
      },
      params: {
        lovGroup: 'DOC_TYPE'
      },
      value: ''
    }),
    new QuestionDropdown({
      key: 'transactionStatus',
      label: CommonConstant.LABEL_STATUS_TTD,
      placeholder: 'Select Status',
      serviceUrl: URLConstant.GetLov,
      options: [
        { key: '', value: 'All' }
      ],
      params: {
        lovGroup: 'LOV_SIGN_STATUS'
      },
      args: {
        list: 'lovList',
        key: 'code',
        value: 'description'
      },
      value: ''
    })
  ],
  params: []
};

export const InquiryTableBranchManager: Table<Document> = {
  name: 'listDocument',
  list: [],
  columns: [
    {
      type: ColumnType.Text,
      prop: 'docTypeName',
      label: 'Doc Type',
      width: 120
    },
    {
      type: ColumnType.Text,
      prop: 'docTemplateName',
      label: 'Doc Name',
      width: 180
    },
    {
      type: ColumnType.Text,
      prop: 'customerName',
      label: CommonConstant.LABEL_CUSTOMER_NAME,
      width: 120
    },
    {
      type: ColumnType.Date,
      format: CommonConstant.FORMAT_DATE_WITHTIME,
      prop: 'requestDate',
      label: CommonConstant.LABEL_REQUEST_DATE,
      width: 140
    },
    {
      type: ColumnType.Date,
      format: CommonConstant.FORMAT_DATE_WITHTIME,
      prop: 'completeDate',
      label: CommonConstant.LABEL_COMPLETE_DATE,
      width: 140
    },
    {
      type: ColumnType.Text,
      prop: 'totalSigned',
      label: CommonConstant.LABEL_TOTAL_SIGNER_SIGNED,
      width: 100
    },
    {
      type: ColumnType.Text,
      prop: 'totalStamped',
      label: CommonConstant.LABEL_TOTAL_MATERAI,
      width: 100
    },
    {
      type: ColumnType.Text,
      prop: 'signStatus',
      label: CommonConstant.LABEL_STATUS_TTD,
      width: 100,
      align: Align.Center,
      class: CommonConstant.TEXT_DANGER,
      condition: true,
      conditionedClass: CommonConstant.TEXT_SUCCESS,
      conditionExpected: 'Complete'
    },
    {
      type: ColumnType.Action,
      label: 'Action',
      width: 140,
      action: [
        {
          class: CommonConstant.TEXT_PRIMARY,
          icon: 'ft-eye',
          type: Act.View,
          descr: 'View'
        },
        {
          class: CommonConstant.TEXT_PRIMARY,
          icon: CommonConstant.ICON_FT_DOWNLOAD,
          type: Act.Download,
          descr: 'Download'
        },
        {
          class: CommonConstant.TEXT_PRIMARY,
          icon: 'ft-user',
          type: Act.ViewSigner,
          descr: 'Signer'
        },
      ]
    }
  ]
};

export const InquiryTableBranchManagerMobile: Table<Document> = {
  name: 'listDocument',
  list: [],
  columns: [
    {
      type: ColumnType.Text,
      cardType: ColumnType.Detail,
      prop: 'docTypeName',
      label: 'Doc Type',
      width: 120
    },
    {
      type: ColumnType.Text,
      cardType: ColumnType.Title,
      prop: 'docTemplateName',
      label: 'Doc Name',
      width: 180
    },
    {
      type: ColumnType.Text,
      cardType: ColumnType.Detail,
      prop: 'customerName',
      label: CommonConstant.LABEL_CUSTOMER_NAME,
      width: 120
    },
    {
      type: ColumnType.Date,
      cardType: ColumnType.Detail,
      format: CommonConstant.FORMAT_DATE_WITHTIME,
      prop: 'requestDate',
      label: CommonConstant.LABEL_REQUEST_DATE,
      width: 140
    },
    {
      type: ColumnType.Date,
      cardType: ColumnType.Detail,
      format: CommonConstant.FORMAT_DATE_WITHTIME,
      prop: 'completeDate',
      label: CommonConstant.LABEL_COMPLETE_DATE,
      width: 140
    },
    {
      type: ColumnType.Text,
      cardType: ColumnType.Detail,
      prop: 'totalSigned',
      label: CommonConstant.LABEL_TOTAL_SIGNER_SIGNED,
      width: 100
    },
    {
      type: ColumnType.Text,
      cardType: ColumnType.Detail,
      prop: 'totalStamped',
      label: CommonConstant.LABEL_TOTAL_MATERAI,
      width: 100
    },
    {
      type: ColumnType.Text,
      prop: 'signStatus',
      label: CommonConstant.LABEL_STATUS_TTD,
      width: 90,
      class: CommonConstant.TEXT_DANGER,
      condition: true, 
      conditionedClass: CommonConstant.TEXT_SUCCESS,
      conditionExpected: 'Complete',
      isStatusCardMobile: true,
    },
    {
      type: ColumnType.Action,
      label: 'Action',
      width: 140,
      action: [ 
        {
          class: CommonConstant.TEXT_PRIMARY,
          icon: 'ft-eye',
          type: Act.View,
          descr: 'View',
          cardAction: true,
          mobileMode: false,
        },
        {
          class: CommonConstant.TEXT_PRIMARY,
          icon: 'ft-user',
          type: Act.ViewSigner,
          descr: 'Signer',
          cardAction: false,
          mobileMode: false,
        },
      ]
    }
  ]
};


export const InquiryTableAdminCredit: Table<Document> = {
  name: 'listDocument',
  list: [],
  columns: [
    {
      type: ColumnType.Text,
      cardType: ColumnType.Detail,
      prop: 'docTypeName',
      label: 'Doc Type',
      width: 120
    },
    {
      type: ColumnType.Text,
      cardType: ColumnType.Title,
      prop: 'docTemplateName',
      label: 'Doc Name',
      width: 200
    },
    {
      type: ColumnType.Text,
      cardType: ColumnType.Detail,
      prop: 'customerName',
      label: CommonConstant.LABEL_CUSTOMER_NAME,
      width: 150
    },
    {
      type: ColumnType.Date,
      cardType: ColumnType.Detail,
      format: CommonConstant.FORMAT_DATE_WITHTIME,
      prop: 'requestDate',
      label: CommonConstant.LABEL_REQUEST_DATE,
      width: 140
    },
    {
      type: ColumnType.Date,
      cardType: ColumnType.Detail,
      format: CommonConstant.FORMAT_DATE_WITHTIME,
      prop: 'completeDate',
      label: CommonConstant.LABEL_COMPLETE_DATE,
      width: 140
    },
    {
      type: ColumnType.Text,
      cardType: ColumnType.Detail,
      prop: 'totalSigned',
      label: CommonConstant.LABEL_TOTAL_SIGNER_SIGNED,
      width: 100
    },
    {
      type: ColumnType.Text,
      cardType: ColumnType.Detail,
      prop: 'totalStamped',
      label: CommonConstant.LABEL_TOTAL_MATERAI,
      width: 100
    },
    {
      type: ColumnType.Text,
      prop: 'signStatus',
      label: CommonConstant.LABEL_STATUS_TTD,
      width: 100,
      class: CommonConstant.TEXT_DANGER,
      condition: true,
      conditionedClass: CommonConstant.TEXT_SUCCESS,
      conditionExpected: 'Complete',
      isStatusCardMobile: true,
    } 
  ]
};
