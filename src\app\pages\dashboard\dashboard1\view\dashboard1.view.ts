import { Input } from "@angular/core";
import { Document } from "app/model/document";
import { Act } from "app/shared/components/msx-card-datatable/enums/act";
import { ColumnType } from "app/shared/components/msx-card-datatable/enums/column-type";
import { Table } from "app/shared/components/msx-card-datatable/models/table";
import { CommonConstant } from "app/shared/constant/common.constant";

export const DashboardTableCustomer: Table<Document> = {
    name: 'listDocument',
    enableSelection: true,
    uniqueKey: 'refNumber',
    list: [],
    columns: [
        {
            type: ColumnType.Text,
            prop: 'docTypeName',
            label: 'Doc Type',
            width: 140
        },
        {
            type: ColumnType.Text,
            prop: 'docTemplateName',
            label: 'Doc Name',
            width: 320
        },
        {
            type: ColumnType.Date,
            format: CommonConstant.FORMAT_DATE_WITHTIME,
            prop: 'requestDate',
            label: CommonConstant.LABEL_REQUEST_DATE,
            width: 140
        },
        {
            type: ColumnType.Text,
            prop: 'totalSigned',
            label: CommonConstant.LABEL_TOTAL_SIGNER_SIGNED,
            width: 130
        },
        {
            type: ColumnType.Text,
            prop: 'totalStamped',
            label: CommonConstant.LABEL_TOTAL_MATERAI,
            width: 130
        },
        {
            type: ColumnType.Text,
            prop: 'signingProcess',
            label: CommonConstant.LABEL_SIGNING_PROCESS,
            width: 130
        },
        {
            type: ColumnType.Action,
            label: 'Action',
            width: 260,
            action: [
                {
                    class: CommonConstant.BUTTON_DANGER,
                    label: CommonConstant.LABEL_SIGNING_DOCUMENT,
                    type: Act.Sign,
                    descr: 'Sign',
                    condition: true,
                    conditionedClass: 'd-none',
                    conditionVariable: 'isCurrentTopPriority',
                    conditionExpected: '0'
                },
                {
                    class: CommonConstant.TEXT_PRIMARY,
                    icon: 'ft-eye',
                    type: Act.View,
                    descr: 'View'
                },
                {
                    class: CommonConstant.TEXT_PRIMARY,
                    icon: CommonConstant.ICON_FT_DOWNLOAD,
                    type: Act.Download,
                    descr: 'Download'
                },
                {
                    class: CommonConstant.TEXT_PRIMARY,
                    icon: 'ft-user',
                    type: Act.ViewSigner,
                    descr: 'Signer'
                },
            ]
        }
    ]
}

export const DashboardTableBranchManager: Table<Document> = {
    name: 'listDocument',
    enableSelection: true,
    uniqueKey: 'refNumber',
    list: [],
    columns: [
        {
            type: ColumnType.Text,
            prop: 'docTypeName',
            label: 'Doc Type',
            width: 120
        },
        {
            type: ColumnType.Text,
            prop: 'docTemplateName',
            label: 'Doc Name',
            width: 180
        },
        {
            type: ColumnType.Text,
            prop: 'customerName',
            label: CommonConstant.LABEL_CUSTOMER_NAME,
            width: 120
        },
        {
            type: ColumnType.Date,
            format: CommonConstant.FORMAT_DATE_WITHTIME,
            prop: 'requestDate',
            label: CommonConstant.LABEL_REQUEST_DATE,
            width: 140
        },
        {
            type: ColumnType.Text,
            prop: 'totalSigned',
            label: CommonConstant.LABEL_TOTAL_SIGNER_SIGNED,
            width: 130
        },
        {
            type: ColumnType.Text,
            prop: 'totalStamped',
            label: CommonConstant.LABEL_TOTAL_MATERAI,
            width: 130
        },
        {
            type: ColumnType.Text,
            prop: 'signingProcess',
            label: CommonConstant.LABEL_SIGNING_PROCESS,
            width: 130
        },
        {
            type: ColumnType.Action,
            label: 'Action',
            width: 260,
            action: [
                {
                    class: CommonConstant.BUTTON_DANGER,
                    label: CommonConstant.LABEL_SIGNING_DOCUMENT,
                    type: Act.Sign,
                    descr: 'Sign',
                    condition: true,
                    conditionedClass: 'd-none',
                    conditionVariable: 'isCurrentTopPriority',
                    conditionExpected: '0'
                },
                {
                    class: CommonConstant.TEXT_PRIMARY,
                    icon: 'ft-eye',
                    type: Act.View,
                    descr: 'View'
                },
                {
                    class: CommonConstant.TEXT_PRIMARY,
                    icon: CommonConstant.ICON_FT_DOWNLOAD,
                    type: Act.Download,
                    descr: 'Download'
                },
                {
                    class: CommonConstant.TEXT_PRIMARY,
                    icon: 'ft-user',
                    type: Act.ViewSigner,
                    descr: 'Signer'
                },
            ]
        }
    ]
}

export const DashboardTableCustomerMobile: Table<Document> = {
    name: 'listDocument',
    enableSelection: true,
    uniqueKey: 'refNumber',
    list: [],
    columns: [
        {
            type: ColumnType.Text,
            cardType: ColumnType.Detail,
            prop: 'docTypeName',
            label: 'Doc Type',
            width: 140
        },
        {
            type: ColumnType.Text,
            cardType: ColumnType.Detail,
            prop: 'docTemplateName',
            label: 'Doc Name',
            width: 320
        },
        {
            type: ColumnType.Date,
            cardType: ColumnType.Detail,
            format: CommonConstant.FORMAT_DATE_WITHTIME,
            prop: 'requestDate',
            label: CommonConstant.LABEL_REQUEST_DATE,
            width: 140
        },
        {
            type: ColumnType.Text,
            cardType: ColumnType.Detail,
            prop: 'totalSigned',
            label: CommonConstant.LABEL_TOTAL_SIGNER_SIGNED,
            width: 130
        },
        {
            type: ColumnType.Text,
            cardType: ColumnType.Detail,
            prop: 'totalStamped',
            label: CommonConstant.LABEL_TOTAL_MATERAI,
            width: 130
        },
        {
            type: ColumnType.Text,
            prop: 'signingProcess',
            label: CommonConstant.LABEL_SIGNING_PROCESS,
            width: 130
        },
        {
            type: ColumnType.Action,
            label: 'Action',
            width: 240,
            action: [
                {
                    class: CommonConstant.BUTTON_DANGER,
                    label: CommonConstant.LABEL_SIGNING_DOCUMENT,
                    type: Act.Sign,
                    descr: 'Sign',  
                    condition: true,
                    conditionedClass: 'd-none',
                    conditionVariable: 'isCurrentTopPriority',
                    conditionExpected: '0',
                    mobileMode: false,
                    cardAction: false, 
                    iconSrc: CommonConstant.ICON_SRC_USER,
                },
                {
                    class: CommonConstant.TEXT_PRIMARY,
                    icon: 'ft-eye',
                    type: Act.View,
                    descr: 'View',
                    mobileMode: false,
                    cardAction: true,
                },
                {
                    class: CommonConstant.TEXT_PRIMARY,
                    icon: CommonConstant.ICON_FT_DOWNLOAD,
                    type: Act.Download,
                    mobileMode: false,
                    cardAction: false,
                    iconSrc: CommonConstant.ICON_SRC_DOWNLOAD,
                    descr: 'Download'
                },
                {
                    class: CommonConstant.TEXT_PRIMARY,
                    icon: 'ft-user',
                    type: Act.ViewSigner,
                    descr: 'Signer',
                    mobileMode: false,
                    cardAction: false,
                    iconSrc: CommonConstant.ICON_SRC_USER,
                },
            ]
        }
    ]
}

export const DashboardTableBranchManagerMobile: Table<Document> = {
    name: 'listDocument',
    enableSelection: true,
    uniqueKey: 'refNumber',
    list: [],
    columns: [
        {
            type: ColumnType.Text, 
            cardType: ColumnType.Detail,
            prop: 'docTypeName',
            label: 'Doc Type',
            width: 120
        },
        {
            type: ColumnType.Text,
            cardType: ColumnType.Detail,
            prop: 'docTemplateName',
            label: 'Doc Name',
            width: 180
        },
        {
            type: ColumnType.Text, 
            cardType: ColumnType.Detail,
            prop: 'customerName',
            label: CommonConstant.LABEL_CUSTOMER_NAME,
            width: 120
        },
        {
            type: ColumnType.Date, 
            cardType: ColumnType.Detail,
            format: CommonConstant.FORMAT_DATE_WITHTIME,
            prop: 'requestDate',
            label: CommonConstant.LABEL_REQUEST_DATE,
            width: 140
        },
        {
            type: ColumnType.Text, 
            cardType: ColumnType.Detail,
            prop: 'totalSigned',
            label: CommonConstant.LABEL_TOTAL_SIGNER_SIGNED,
            width: 130
        },
        {
            type: ColumnType.Text,
            cardType: ColumnType.Detail,
            prop: 'totalStamped',
            label: CommonConstant.LABEL_TOTAL_MATERAI,
            width: 130
        },
        {
            type: ColumnType.Text,
            prop: 'signingProcess',
            label: CommonConstant.LABEL_SIGNING_PROCESS,
            width: 130
        },
        {
            type: ColumnType.Action,
            label: 'Action',
            width: 240,
            action: [
                {
                    class: CommonConstant.BUTTON_DANGER,
                    label: CommonConstant.LABEL_SIGNING_DOCUMENT,
                    type: Act.Sign,
                    descr: 'Sign',
                    condition: true,
                    conditionedClass: 'd-none',
                    conditionVariable: 'isCurrentTopPriority',
                    conditionExpected: '0', 
                    mobileMode: false,
                    cardAction: false, 
                    iconSrc: CommonConstant.ICON_SRC_USER,
                },
                {
                    class: CommonConstant.TEXT_PRIMARY,
                    icon: 'ft-eye',
                    type: Act.View,
                    descr: 'View', 
                    mobileMode: false,
                    cardAction: true,
                },
                {
                    class: CommonConstant.TEXT_PRIMARY,
                    icon: CommonConstant.ICON_FT_DOWNLOAD,
                    type: Act.Download, 
                    descr: 'Download',
                    mobileMode: false,
                    cardAction: false,
                    iconSrc: CommonConstant.ICON_SRC_DOWNLOAD
                },
                {
                    class: CommonConstant.TEXT_PRIMARY,
                    icon: 'ft-user',
                    type: Act.ViewSigner,
                    descr: 'Signer',  
                    mobileMode: false,
                    cardAction: false,
                    iconSrc: CommonConstant.ICON_SRC_USER,
                },
            ]
        }
    ]
}
