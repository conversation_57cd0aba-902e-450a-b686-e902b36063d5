import { Component, OnInit } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { RetryStampingRequest } from 'app/model/api/retry-stamping.request';
import { MonitoringEmeteraiNormal } from 'app/model/monitoring-emeterai-normal';
import { MonitoringService } from 'app/services/api/monitoring.service';
import { FormConstant } from 'app/shared/components/ms-form/constants/form.constant';
import { FormModel } from 'app/shared/components/ms-form/models';
import { QuestionDate, QuestionDropdown, QuestionTextbox } from 'app/shared/components/ms-form/questions';
import { Act } from 'app/shared/components/msx-datatable/enums/act';
import { ColumnType } from 'app/shared/components/msx-datatable/enums/column-type';
import { Table } from 'app/shared/components/msx-datatable/models/table';
import { MsxView } from 'app/shared/components/msx-view/models/MsxView';
import { WidgetType } from 'app/shared/components/msx-view/models/WidgetType';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { GlobalService } from 'app/shared/data/global.service';
import { DownloadExcelTaxReportRequest } from 'app/shared/dto/Monitoring-e-materai/download-excel-tax-report.request';
import { RetryLatestStampinFromUploadRequest } from 'app/shared/dto/Monitoring-e-materai/retry-stamping-from-upload.request';
import { Tools } from 'app/shared/tools/Tools';
import { saveAs } from 'file-saver';
import * as swalFunction from '../../../shared/data/sweet-alerts';
import { StampdutyErrorComponent } from '../monitoring-embed/stampduty-error/stampduty-error.component';
import { ViewDocumentRequest } from 'app/model/api/view.document.request';
import { ViewDocumentResponse } from 'app/model/api/view.document.response';
import { HttpClient } from '@angular/common/http';
import { PathConstant } from 'app/shared/constant/PathConstant';
import { Router } from '@angular/router';
import { DeviceDetectorService } from 'ngx-device-detector';
import { CommonConstant } from 'app/shared/constant/common.constant';
import { RetryStampingMeteraiRequest } from 'app/shared/dto/inquiry/retry-stamping.request';

@Component({
  selector: 'app-monitoring-normal',
  templateUrl: './monitoring-normal.component.html',
  styleUrls: ['./monitoring-normal.component.scss']
})
export class MonitoringNormalComponent implements OnInit {
  view: MsxView;
  searchFormObj: FormModel<any>;
  serviceUrl = URLConstant.ListMonitoring;
  swal = swalFunction;
  isMobile = false;

  constructor(private global: GlobalService, private monitoringService: MonitoringService,
    private modalService: NgbModal, private http: HttpClient,private router: Router,
    private deviceService: DeviceDetectorService) {
      this.isMobile = deviceService.isMobile();
    }

  ngOnInit(): void {
    this.initView();
  }

  initView() {
    this.searchFormObj = {
      name: 'monitoringEmeteraiSearchForm',
      direction: FormConstant.DIRECTION_HORIZONTAL,
      colSize: 6,
      exportExcel: false,
      exportExcelLabel: 'Export Tax Excel',
      components: [
        new QuestionTextbox({
          key: 'nomorDokumen',
          label: CommonConstant.LABEL_DOCUMENT_NUMBER,
          placeholder: 'Type nomor document here',
          value: ''
        }),
        new QuestionDropdown({
          key: 'jenisDokumen',
          label: 'Document Peruri Type',
          placeholder: 'Select document kind',
          serviceUrl: URLConstant.ListPeruriDocumentType,
          options: [
            { key: '', value: 'All' }
          ],
          value: '',
          args: {
            list: 'documentEMateraiList',
            key: 'documentName',
            value: 'documentName'
          },
          params: {}
        }),
        new QuestionDropdown({
          key: 'tipeDokumen',
          label: 'Document Type',
          placeholder: 'Select Document Type',
          serviceUrl: URLConstant.GetLov,
          options: [
            { key: '', value: 'All' }
          ],
          value: '',
          args: {
            list: 'lovList',
            key: 'code',
            value: 'description'
          },
          params: {
            lovGroup: 'DOC_TYPE'
          }
        }),
        new QuestionDropdown({
          key: 'templateDokumen',
          label: 'Document Template',
          placeholder: 'Select document template',
          serviceUrl: URLConstant.ListTemplate,
          options: [
            { key: '', value: 'All' },
            { key: 'MANUAL', value: 'MANUAL'}
          ],
          value: '',
          args: {
            list: 'listDocumentTemplate',
            key: 'documentTemplateName',
            value: 'documentTemplateName'
          },
          params: {
            tenantCode: this.global.user.role.tenantCode
          }
        }),
        new QuestionDate({
          key: 'tanggalDokumenMulai',
          label: 'Document Date From',
          placeholder: CommonConstant.FORMAT_DATE
        }),
        new QuestionDate({
          key: 'tanggalDokumenSampai',
          label: 'Document Date To',
          placeholder: CommonConstant.FORMAT_DATE
        }),
        new QuestionDropdown({
          key: 'hasilStamping',
          label: 'Stamping Result',
          placeholder: 'Select stamping result',
          options: [
            {key: '', value: 'All'},
            {key: 'Not Started', value: 'Not Started'},
            {key: 'Failed', value: 'Failed'},
            {key: 'In Progress', value: 'In Progress'},
            {key: 'Success', value: 'Success'},
          ],
        }),
        new QuestionTextbox({
          key: 'noSN',
          label: 'Serial Number',
          placeholder: 'Type serial number here',
          value: ''
        }),
        new QuestionDropdown({
          key: 'cabang',
          label: 'Office',
          placeholder: 'Select office',
          serviceUrl: URLConstant.OfficeList,
          options: [
            { key: '', value: 'All' }
          ],
          value: '',
          args: {
            list: 'officeList',
            key: 'officeCode',
            value: 'officeName'
          },
          params: {
            tenantCode: this.global.user.role.tenantCode
          }
        }),
        new QuestionDropdown({
          key: 'taxType',
          label: 'Tax Type',
          placeholder: 'Select tax type',
          options: [
            { key: '', value: 'All' },
            { key: 'Pemungut', value: 'Pemungut' },
            { key: 'Non Pemungut', value: 'Non Pemungut' },
          ]
        })
      ],
      params: [
        {
          key: 'tenantCode',
          controlType: FormConstant.TYPE_HIDDEN,
          value: this.global.user.role.tenantCode
        },
        {
          key: 'page',
          controlType: FormConstant.TYPE_HIDDEN,
          value: 1
        }
      ]
    };

    const monitoringTable: Table<MonitoringEmeteraiNormal> = {
      name: 'listMonitoring',
      list: [],
      columns: [
        {
          type: ColumnType.Text,
          prop: 'nomorDokumen',
          label: CommonConstant.LABEL_DOCUMENT_NUMBER,
          width: 100
        },
        {
          type: ColumnType.Date,
          format: 'DD-MMM-YYYY',
          prop: 'tanggalDokumen',
          label: 'Document Date',
          width: 80
        },
        {
          type: ColumnType.Text,
          prop: 'cabang',
          label: 'Office',
          width: 80
        },
        {
          type: ColumnType.Text,
          prop: 'namaDokumen',
          label: 'Document Name',
          width: 80
        },
        {
          type: ColumnType.Text,
          prop: 'jenisDokumen',
          label: 'Document Peruri Type',
          width: 80
        },
        {
          type: ColumnType.Text,
          prop: 'tipeDokumen',
          label: 'Document Type',
          width: 80
        },
        {
          type: ColumnType.Currency,
          prop: 'nominalDokumen',
          label: 'Document Nominal',
          width: 70
        },
        {
          type: ColumnType.Text,
          prop: 'templateDokumen',
          label: 'Document Template',
          width: 80
        },
        {
          type: ColumnType.Text,
          prop: 'hasilStamping',
          label: 'Stamping Result',
          width: 70
        },
        {
          type: ColumnType.Text,
          prop: 'noSN',
          label: 'Serial Number',
          width: 110
        },
        {
          type: ColumnType.Text,
          prop: 'prosesMaterai',
          label: 'Meterai Process',
          width: 60
        },
        {
          type: ColumnType.Text,
          prop: 'taxType',
          label: 'Tax Type',
          width: 80
        },
        {
          type: ColumnType.Action,
          label: 'Action',
          width: 85,
          action: [
            {
              class: CommonConstant.TEXT_PRIMARY,
              icon: 'ft-alert-triangle',
              descr: 'View Error Message',
              type: Act.View,
              condition: true,
              conditionVariable: 'errorMessage',
              conditionExpected: '',
              conditionedClass: 'd-none'
            },
            {
              class: CommonConstant.TEXT_PRIMARY,
              icon: 'ft-play-circle',
              descr: 'Retry Stamping',
              type: Act.Resend,
              condition: true,
              conditionVariable: 'errorMessage',
              conditionExpected: '',
              conditionedClass: 'd-none'
            },
            // {
            //   class: CommonConstant.TEXT_PRIMARY,
            //   icon: 'ft-rotate-cw',
            //   descr: 'Retry Stamping From Upload',
            //   type: Act.Resend,
            //   condition: true,
            //   conditionVariable: 'taxType',
            //   conditionExpected: 'Non Pemungut',
            //   conditionedClass: 'd-none'
            // },
            {
              class: CommonConstant.TEXT_PRIMARY,
              icon: 'ft-eye',
              type: Act.View,
              descr: 'View Document'
            },
            {
              class: CommonConstant.TEXT_PRIMARY,
              icon: CommonConstant.ICON_FT_DOWNLOAD,
              type: Act.Download,
              descr: 'Download'
            },
          ]
        }
      ]
    };

    this.view = {
      title: 'Monitoring E-Meterai',
      components: [
        {
          type: WidgetType.SearchFilter,
          component: this.searchFormObj
        },
        {
          type: WidgetType.Datatable,
          component: monitoringTable
        }
      ]
    }
  }

  exportExcel(params: any) {
    const request = new DownloadExcelTaxReportRequest();
    request.tenantCode = this.global.user.role.tenantCode;
    request.nomorDokumen = params.nomorDokumen;
    request.tipeDokumen = params.tipeDokumen;
    request.jenisDokumen = params.jenisDokumen;
    request.templateDokumen = params.templateDokumen;
    request.hasilStamping = params.hasilStamping;
    request.noSN = params.noSN;
    request.tanggalDokumenMulai = Tools.dateToString(params.tanggalDokumenMulai);
    request.tanggalDokumenSampai = Tools.dateToString(params.tanggalDokumenSampai);
    request.cabang = params.cabang;
    request.taxType = params.taxType;

    this.monitoringService.downloadExcelTaxReportNormal(request).subscribe(
      (response) => {
        if (response.status.code === 0) {
          const blob = Tools.b64toBlob(response.base64ExcelReport);
          saveAs(blob, response.filename);
        }
      }
    )
  }

  onItemClickListener(result: any) {
    const data = result['data'];
    const action = result['act']['descr'];

    switch(action) {
      case 'View Error Message':
        return this.viewErrorMessage(data);
      case 'Retry Stamping':
        return this.retryStamping(data);
      case 'Retry Stamping From Upload':
        return this.retryStampingFromUpload(data);
      case 'View Document':
        return this.openDocument(data, false);
      case 'Download':
        return this.openDocument(data, true);
    }
  }

  openDocument(data, download: boolean) {
    const request = new ViewDocumentRequest();
    request.documentId = data.documentId;
    this.http.post<ViewDocumentResponse>(URLConstant.ViewDocument, request).subscribe(
      (response) => {
        if (response.status.code !== 0) {
          return;
        }

        if (this.isMobile) {
          const filename = data.docTemplateName + '.pdf';
          const element = document.createElement('a');
          element.href = `data:application/pdf;base64,${response.pdfBase64}`;
          element.download = filename;
          element.click();
          return;
        }

        if (download) {
          const filename = data.namaDokumen + '.pdf';
          const element = document.createElement('a');
          element.href = `data:application/pdf;base64,${response.pdfBase64}`;
          element.download = filename;
          element.click();
          return;
        }
        const extras = { pdfBase64: response.pdfBase64, refNumber: data.nomorDokumen };
        data = { ...data, ...extras };
        this.router.navigate([PathConstant.VIEW_DOCUMENT_INQUIRY], { state: data });
      }
    )
  }

  viewErrorMessage(data) {
    const modal = this.modalService.open(StampdutyErrorComponent, { backdrop: 'static', keyboard: false, size: 'l' });
    modal.componentInstance.errorMessage = data['errorMessage'];
  }

  retryStamping(data) {
    this.swal.Confirm('Apakah Anda yakin ingin melakukan proses retry stamping?').then(
      (result) => {
        if (!result.isConfirmed) {
          return;
        }

        const request = new RetryStampingMeteraiRequest();
        request.refNumber = data['nomorDokumen'];
        request.tenantCode = this.global.user.role.tenantCode;

        this.monitoringService.retryStampingMeterai(request).subscribe(
          (response) => {
            if (response.status.code === 0) {
              this.swal.Success('Retry stamping berhasil dimulai').then(() => {
                window.location.reload();
              });
            }
          }
        )
      }
    )
  }

  retryStampingFromUpload(data) {
    this.swal.Confirm('Apakah Anda yakin ingin memproses stamping dengan upload ulang?').then(
      (result) => {
        if (!result.isConfirmed) {
          return;
        }

        const request = new RetryLatestStampinFromUploadRequest();
        request.refNumber = data['nomorDokumen'];
        request.tenantCode = this.global.user.role.tenantCode;
        
        this.monitoringService.retryLatestStampFromUpload(request).subscribe(
          (response) => {
            if (response.status.code === 0) {
              this.swal.Success(response.status.message).then(() => {
                window.location.reload();
              });
            }
          }
        )
      }
    )
  }

}
