import {ChangeDete<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ement<PERSON>ef, NgZone, On<PERSON>estroy, OnInit, ViewChild} from '@angular/core';
import {FormBuilder, FormGroup, ValidationErrors, Validators} from '@angular/forms';
import {ActivatedRoute, Router} from '@angular/router';
import {UserService} from '../../../services/api/user.service';
import * as moment from 'moment';
import {RegisterRequest} from '../../../model/api/register.request';
import {UserProfile} from '../../../model/user-profile';
import {ToastrService} from 'ngx-toastr';
import {GlobalService} from '../../../shared/data/global.service';
import {NgbModal} from '@ng-bootstrap/ng-bootstrap';
import {CameraComponent} from '../../../shared/components/camera/camera.component';
import {VerificationEmailComponent} from '../verification-email/verification-email.component';
import {PathConstant} from 'app/shared/constant/PathConstant';
import {DataService} from 'app/services/api/data.service';
import {VendorListRequest} from 'app/model/api/vendor-list.request';
import {CommonConstant} from 'app/shared/constant/common.constant';
import {URLConstant} from 'app/shared/constant/URLConstant';
import {NgxSpinnerService} from 'ngx-spinner';
import {CheckUserEmailServiceRequest} from '../../../shared/dto/user/check-user-email-service.request';
import {CameraKtpComponent} from 'app/shared/components/camera-ktp/camera-ktp.component';
import {VendorListResponse} from '../../../shared/dto/data/vendor-list.response';
import {CroppedEvent, imageFormat} from 'ngx-photo-editor';
import * as swalFunction from '../../../shared/data/sweet-alerts';
import {MsxAlertComponent} from '../../../shared/components/msx-alert/msx-alert.component';
import {Province} from '../../../model/province';
import {DataRequest} from '../../../model/api/data.request';
import {Vendor} from '../../../shared/constant/vendor';
import {City} from '../../../model/city';
import {District} from '../../../model/district';
import {ReregisterModalComponent} from './reregister-modal/reregister-modal.component';
import { CountlyService } from 'app/services/api/countly.service';
import { PrivyLivenessComponent } from 'app/pages/privy-liveness/privy-liveness.component';

@Component({
  selector: 'app-register-page',
  templateUrl: './register-page.component.html',
  styleUrls: ['./register-page.component.scss']
})

export class RegisterPageComponent implements OnInit, OnDestroy {

  @ViewChild('formElement') formElement: ElementRef;

  formFields = {
    psre: 'psre',
    province: 'province',
    city: 'city'
  };

  imgState: {size: number, key: string};
  registerFormSubmitted = false;
  registerForm: FormGroup;
  imageChangedEvent: any;
  base64: any;
  imageFormat: imageFormat = 'jpeg';
  imageName: string;
  sizeMap: string[];
  messages  = [];
  keys      = [];

  comboMode = false;
  listProvince: Province[];
  selectedProvince: any = null;
  comboProvince: any;
  listCity: City[] = [];
  selectedCity: any = null;
  listDistrict: District[] = [];
  selectedDistrict: any = null;
  provider = [];

  swal = swalFunction;

  answers = [
    { id: 1, name: 'Belum pernah' },
    { id: 2, name: 'Sudah pernah' }
  ];

  genders = [
    { id: 'M', name: 'Male' },
    { id: 'F', name: 'Female' },
  ];

  fields: any;
  validationMessages: any;
  template: any;

  registerRequest: RegisterRequest;
  registerMsg: string;
  userProfile: UserProfile;
  selectedProvider: any;
  selectedGender = '';
  selectedAnswer: any;
  state = 'register';
  loginId: string;
  tenantCode: string;
  vendorCode: string;
  fileName: string;
  src: string;
  routeData: any;
  isEmailFromEsign = false;
  disableBrowse = false;
  disableTakePhotoKTP = false;
  disableTakePhoto = false;
  msg: string;
  idno: string;
  email: string;
  phone: string;
  psreCode: string;
  urlLivenessPrivy: string;

  tmpUser: UserProfile;

  public photoWebcam: string;

  constructor(private formBuilder: FormBuilder, private router: Router, private readonly userService: UserService,
    private cdr: ChangeDetectorRef, private toastrService: ToastrService, private readonly global: GlobalService,
    private modalService: NgbModal, private activeRoute: ActivatedRoute, private ngZone: NgZone,
    private dataService: DataService, private spinner: NgxSpinnerService, private ngModal: NgbModal,
    private countlyService: CountlyService
    ) {
    this.listProvince = [{idMsProvinsi: 1, namaProvinsi: 'JAWA BARAT'}];
    this.sizeMap = [];
    this.src = '';
    this.activeRoute.queryParamMap.subscribe(params => {
      const state = this.router.getCurrentNavigation().extras.state;
      this.routeData = this.activeRoute.snapshot.data;
      this.src = params.get('code') ? 'INVITATION' : this.routeData['src'] || '';
      console.log('Params', params);
      console.log('State', state);

      this.loginId = params.get('email') || this.global.user?.loginId;
      this.tenantCode = (state ? state.data.tenantCode : params.get('tenantCode')) || localStorage.getItem('tenantCode');
      this.vendorCode = (state ? state.data.vendorCode : params.get('vendorCode')) || this.getLocalVendorCode();
      this.msg = (state ? state.data.msg : params.get('msg'));

      if (this.src === 'INVITATION') {
        this.msg = params.get('code');
      }

      localStorage.setItem('tenantCode', this.tenantCode);
      localStorage.setItem('vendorCode', this.vendorCode);

      this.selectedProvider = this?.vendorCode;
    });

    this.userProfile = new UserProfile();
    console.log('Tenant code: ', this.tenantCode);
    console.log('Vendor code: ', this.vendorCode);
  }

  ngOnDestroy(): void {
    this.messages = [];
    this.keys     = [];
    localStorage.removeItem('oldEmail');
    localStorage.removeItem('timeLeft');
  }

  getLocalVendorCode() {
    return localStorage.getItem('vendorCode') === null ? '' : localStorage.getItem('vendorCode');
  }

  async getVendorList() {
    let url;
    const request = new VendorListRequest();
    request.audit = { callerId: '' };
    if (this.vendorCode && this.src !== 'INVITATION') {
      request.vendorCode = this.vendorCode;
    }

    switch (this.src) {
      case 'CONFINS':
        request.msg = this.msg;
        url = URLConstant.GetVendorListEmbed;
        break;
      case 'INVITATION':
        request.msg = this.msg;
        url = URLConstant.GetVendorByCode;
        break;
      default:
        request.tenantCode = this.tenantCode;
        url = URLConstant.GetVendorList;
        break;
    }

    let resp: VendorListResponse = new VendorListResponse();

    request.vendorTypeCode = CommonConstant.VENDOR_TYPE_PSRE;
    await this.dataService.getVendorList(request, url).toPromise().then(
      (response) => {
        resp = response;
        console.log('Vendor resp', resp);
        if (response.status.code === 0 && response.documentId) {
          this.router.navigate([PathConstant.SIGNATURE], {queryParams: {
              id: response.documentId
            }});
        }

        if (response.status.code === 0) {
          response.vendorList.forEach(vendor => {
            this.provider = [...this.provider, { id: vendor.code, name: vendor.name }];
          });

          if (this.provider.length === 1) {
            this.selectedProvider = this.provider[0];
            console.log('selectedProvider', this.selectedProvider);
            this.vendorCode = this.selectedProvider.id
            // this.onChange('psre', this.selectedProvider);
          }
          this.initView();
        } else if (response.status.code === CommonConstant.STATUS_CODE_INACTIVE_LINK) {
          this.state = 'link-inactive';
          console.log('state', this.state);
        } else {
          this.router.navigate([PathConstant.LOGIN]);
        }
      }
    );

    return resp;
  }

  async ngOnInit() {
    if (this.src === 'CONFINS'){
      localStorage.clear();
    }

    if (this.src === 'CONFINS' || this.src === 'INVITATION')  {
      // Remove token on invitation and embed
      localStorage.removeItem('token');
      this.global.token = null;
    }

    localStorage.removeItem('msg');
    if (this.src === 'CONFINS') {
      localStorage.removeItem('token');
      localStorage.clear();
    }
    if (this.msg) {
      this.global.msg = this.msg;
    }
    await this.getVendorList();
    this.countlyService.initiate();
  }

  disableRegisterInput() {
    if (this.registerForm.controls['name'].value) {
      this.registerForm.controls['name'].disable();
    }
    if (this.registerForm.controls['email'].value) {
      this.registerForm.controls['email'].disable();
    }
    if (this.registerForm.controls['pob'].value) {
      this.registerForm.controls['pob'].disable();
    }
    if (this.registerForm.controls['dob'].value) {
      this.registerForm.controls['dob'].disable();
    }
    if (this.registerForm.controls['gender'].value) {
      this.registerForm.controls['gender'].disable();
    }
    if (this.registerForm.controls['phone'].value) {
      this.registerForm.controls['phone'].disable();
    }
    if (this.registerForm.controls['psre'].value && this.provider.length === 1) {
      this.registerForm.controls['psre'].disable();
    }
    if (this.registerForm.controls['nik'].value) {
      this.registerForm.controls['nik'].disable();
    }
    if (this.registerForm.controls['address'].value) {
      this.registerForm.controls['address'].disable();
    }
    if (this.registerForm.controls['district'].value) {
      this.registerForm.controls['district'].disable();
    }
    if (this.registerForm.controls['subDistrict'].value) {
      this.registerForm.controls['subDistrict'].disable();
    }
    if (this.registerForm.controls['city'].value) {
      this.registerForm.controls['city'].disable();
    }
    if (this.registerForm.controls['province'].value) {
      this.registerForm.controls['province'].disable();
    }
    if (this.registerForm.controls['zip'].value) {
      this.registerForm.controls['zip'].disable();
    }
    if (this.registerForm.controls['fotoKtp'].value) {
      this.disableBrowse = true;
      this.disableTakePhotoKTP = true;
    }
    if (this.registerForm.controls['fotoSelfie'].value) {
      this.disableTakePhoto = true;
    }
    this.cdr.detectChanges();
  }

  initView() {
    if (this.src === '') {
      this.userService.checkUserActivationStatus(this.loginId).subscribe(
        response => {
          if (response.activeStatus === '1') {
            this.router.navigate([PathConstant.DASHBOARD], { queryParams: { isReload: true } });
          } else if (response.activeStatus === '0' && response.registrationStatus === '1') {
            this.router.navigate([PathConstant.ACTIVATION],
              { queryParams: { uri: this.global.user.loginId, tenantCode: this.tenantCode, vendorCode: this.vendorCode } });
          }
        }
      );
    }

    this.setProperties();
    this.registerForm = this.formBuilder.group({
      name: ['', Validators.compose([Validators.required, Validators.maxLength(50)])],
      nik: ['', Validators.compose([Validators.required, Validators.maxLength(16), Validators.pattern('\\d*')])],
      pob: ['', Validators.compose([Validators.required, Validators.maxLength(50)])],
      dob: ['', Validators.compose([Validators.required, Validators.maxLength(10)])],
      gender: ['', Validators.required],
      phone: ['', Validators.compose([Validators.required, Validators.maxLength(15)])],
      address: ['', Validators.required],
      subDistrict: ['', Validators.compose([Validators.required, Validators.maxLength(50)])],
      district: ['', Validators.compose([Validators.required, Validators.maxLength(50)])],
      city: ['', Validators.compose([Validators.required, Validators.maxLength(50)])],
      province: ['', Validators.compose([Validators.required, Validators.maxLength(50)])],
      zip: ['', Validators.compose([Validators.required, Validators.maxLength(5), Validators.pattern('\\d*')])],
      psre: ['', Validators.required],
      fotoSelfie: ['', Validators.required],
      fotoKtpRaw: [''],
      fotoKtp: ['', Validators.compose([Validators.required, Validators.minLength(1)])],
      email: ['', Validators.compose([
        Validators.required,
        Validators.pattern('[\\w\\-\\._]+@[\\w\\-\\._]+\\.\\w{2,10}'),
        Validators.maxLength(64)]
      )],
      acceptTerms: [false, Validators.requiredTrue],
      acceptTermsMitra: [''],
      acceptTermsVida: [''],
      acceptTermsPrivy: [''],
    });

    if (this.src === '') {
      this.userService.userData(this.loginId, '1').subscribe(response => {
        const role = this.global.user.role;
        if (response.status.code === 0) {
          this.tmpUser = response.userData;
          this.global.user = response.userData;
          this.registerForm.patchValue({
            psre: response.vendorCode,
            name: response.userData.nama,
            email: response.userData.email,
            pob: response.userData.tmpLahir,
            dob: this.parseDate(response.userData.tglLahir),
            gender: response.userData.jenisKelamin,
            phone: response.userData.tlp,
            nik: response.userData.idKtp,
            address: response.userData.alamat,
            district: response.userData.kecamatan,
            subDistrict: response.userData.kelurahan,
            city: response.userData.kota,
            province: response.userData.provinsi,
            zip: response.userData.kodePos,
            fotoKtp: (response.userData.idPhoto) ? CommonConstant.DATA_IMAGE_JPEG_BASE64 + response.userData.idPhoto : null,
            fotoSelfie: (response.userData.selfPhoto) ? CommonConstant.DATA_IMAGE_JPEG_BASE64 + response.userData.selfPhoto : null
          });
          this.selectedGender = response.userData.jenisKelamin;
          this.disableRegisterInput();
        }
        const user = this.global.user;
        user.role = role;
        this.global.user = user;
        this.psreCode = response.vendorCode;
        if(this.psreCode === 'VIDA'){
          this.registerForm.get('acceptTermsMitra').setValidators(Validators.requiredTrue);
          this.registerForm.get('acceptTermsVida').setValidators(Validators.requiredTrue);
        }
        if (this.psreCode === Vendor.PRIVY) {
          this.registerForm.get('acceptTermsMitra').setValidators(Validators.requiredTrue);
          this.disableBrowse = true;
        }

        const tmpPsre = this.provider.find(x => x.id === response.vendorCode);
        console.log('tmpPsre', tmpPsre);
        this.selectedProvider = tmpPsre;
        console.log('set tmpUser', this.tmpUser);

        this.cdr.detectChanges();
      });
    } else if (this.src === 'INVITATION') {
      this.userService.getUserByCode(this.msg).subscribe(response => {
        if (response.status.code === 0) {
          this.tenantCode = response.tenantCode;
          localStorage.setItem('tenantCode', response.tenantCode);
          this.checkInvitationRegisterStatus(response.userData.email);
          this.urlLivenessPrivy = response.livenessUrl;
          this.tmpUser = response.userData;
          this.global.user = response.userData;
          this.registerForm.patchValue({
            psre: response.vendorCode,
            name: response.userData.nama,
            email: response.userData.email,
            pob: response.userData.tmpLahir,
            dob: this.parseDate(response.userData.tglLahir),
            gender: response.userData.jenisKelamin,
            phone: response.userData.tlp,
            nik: response.userData.idKtp,
            address: response.userData.alamat,
            district: response.userData.kecamatan,
            subDistrict: response.userData.kelurahan,
            city: response.userData.kota,
            province: response.userData.provinsi,
            zip: response.userData.kodePos,
            fotoKtp: (response.userData.idPhoto) ? CommonConstant.DATA_IMAGE_JPEG_BASE64 + response.userData.idPhoto : null,
            fotoSelfie: (response.userData.selfPhoto) ? CommonConstant.DATA_IMAGE_JPEG_BASE64 + response.userData.selfPhoto : null
          });

          if (!response.userData.email && response.userData.tlp) {
            this.registerForm.get('email').clearValidators();
            this.registerForm.get('email').disable();
            this.registerForm.updateValueAndValidity();
          }

          const tmpPsre = this.provider.find(x => x.id === response.vendorCode);
          console.log('tmpPsre', tmpPsre);

          this.selectedProvider = tmpPsre;
          this.selectedGender = response.userData.jenisKelamin;
          this.disableRegisterInput();
          this.registerForm.controls['psre'].disable();
          this.psreCode = response.vendorCode;
          if(this.psreCode === 'VIDA'){
            this.registerForm.get('acceptTermsMitra').setValidators(Validators.requiredTrue);
            this.registerForm.get('acceptTermsVida').setValidators(Validators.requiredTrue);
          }

          if(this.psreCode === Vendor.TekenAja || this.psreCode === Vendor.PRIVY) {
            this.registerForm.get('acceptTermsMitra').setValidators(Validators.requiredTrue);
          }

          if (this.psreCode === Vendor.PRIVY) {
            this.disableBrowse = true;
          }

          this.onChange('psre', this.selectedProvider);
          console.log('selectedProvider', response.vendorCode);
          console.log('regisForm', this.registerForm.getRawValue());
          this.cdr.detectChanges();
        }
      })
    }
  }

  checkInvitationRegisterStatus(email: string) {
    if (this.src !== 'INVITATION') {
      return;
    }
    
    this.userService.checkInvitationRegisterStatus(this.msg).subscribe(response => {
        
      // Response code invitation expired
      if (response.status.code === 7023) {
        this.state = 'link-expired';
        return;
      }
      
      // Response code success
      if (response.status.code === 0) {

        if (response.certificateActiveStatus === '0') {
          return;
        }

        // Go to login
        if (response.activeStatus === '1') {
          this.router.navigate([PathConstant.LOGIN]);
          return;
        }

        // External activation modal
        if (response.activationByExternalFlow === '1') {
          this.openExternalActivationPopup();
          return;
        }
        
        // Go to activation
        if (response.registrationStatus === '1') {
          const user: UserProfile = new UserProfile();
          user.pathSrc = CommonConstant.PATH_SRC_INV;
          this.global.user = user;
          const actParams  = {status: 1, params: { src: this.src, uri: email, msg: this.msg, vendorCode: this.selectedProvider['id'] || this.selectedProvider }};
          localStorage.setItem(this.msg, JSON.stringify(actParams));
          this.gotoActInv(actParams.params);
          return;
        }

        // Verification in progress
        if (response.verificationInProgress === '1') {
          const modal = this.modalService.open(MsxAlertComponent, { backdrop: 'static', keyboard: false, size: 'm' });
          modal.componentInstance.message = 'Progress verifikasi registrasi Anda sedang diproses. Harap menunggu sampai proses verifikasi selesai sebelum melanjutkan ke proses berikutnya.';
          modal.componentInstance.hideBtn = true;
        }

        // Verification failed
        if (response.verificationInProgress === '2') {
          const modal = this.modalService.open(MsxAlertComponent, { backdrop: 'static', keyboard: false, size: 'm' });
          modal.componentInstance.message = response.verificationResult + ' Silahkan mencoba registrasi kembali dengan data yang sesuai.';
          modal.componentInstance.btnLabel = 'Lanjut';
          modal.componentInstance.hideBtn = false;
        }
      }
    });
  }

  get rf() {
    return this.registerForm.controls;
  }

  //  On submit click, reset field value
  onSubmit() {
    this.state = 'register';
    // this.router.navigate(['/pages/login']);
  }

  checkImageValues() {
    const formData = this.registerForm.getRawValue();
    if (formData.fotoSelfie === '') {
      this.registerForm.controls['fotoSelfie'].markAsDirty();
    }
    if (formData.fotoKtp === '') {
      this.registerForm.controls['fotoKtp'].markAsDirty();
    }
  }

  async onRegister() {
    console.log('clicked');
    this.checkImageValues();
    if (this.registerForm.invalid) {
      console.log('Form Data', this.registerForm.getRawValue());
      console.log('checkProvince', this.selectedProvince);
      console.log('comboProvince', this.comboProvince);
      this.getFormValidationErrors();
      const modal = this.modalService.open(MsxAlertComponent, { backdrop: 'static', keyboard: false, size: 'sm' });
      modal.componentInstance.title = 'Validation Error';
      modal.componentInstance.listMessages = this.messages;
      modal.componentInstance.hideBtn = false;
      return;
    }

    // Call Register user api
    const formData = this.registerForm.getRawValue();
    this.userProfile.nama = formData.name;
    this.userProfile.email = formData.email;
    this.email = formData.email;
    this.userProfile.tmpLahir = formData.pob;
    this.userProfile.tglLahir = formData.dob;
    this.userProfile.jenisKelamin = formData.gender;
    this.userProfile.tlp = formData.phone;
    this.phone = formData.phone;
    this.userProfile.idKtp = formData.nik;
    this.idno = formData.nik;
    this.userProfile.alamat = formData.address;
    this.userProfile.kecamatan = formData.district;
    this.userProfile.kelurahan = formData.subDistrict;
    this.userProfile.kota = formData.city;
    this.userProfile.provinsi = formData.province;
    this.userProfile.kodePos = formData.zip;
    this.userProfile.psreCode = formData.psre.id;
    this.userProfile.selfPhoto = formData.fotoSelfie;
    this.userProfile.idPhoto = formData.fotoKtp;
    this.userProfile.redirect = true;
    this.vendorCode = formData.psre;

    // Update register form untuk vendor tekenAja
    if (this.selectedProvider['id'] === Vendor.TekenAja && this.src !== 'INVITATION') {
      console.log('updateUserProfile', this.selectedProvince);
      this.userProfile.provinsi = this.selectedProvince['namaProvinsi'] || this.comboProvince['namaProvinsi'];
      this.userProfile.provinceId = this.selectedProvince['idMsProvinsi'] || this.comboProvince['idMsProvinsi'];
      this.userProfile.kota = formData.city['districtName'];
      this.userProfile.districtId = Number(formData.city['idMsDistrict']);
      this.userProfile.kecamatan = formData.district['subDistrictName'];
      this.userProfile.subdistrictId = Number(formData.district['idMsSubDistrict']);
    }

    this.registerRequest = new RegisterRequest();
    this.registerRequest.userData = this.userProfile;
    if (this.msg) {
      this.registerRequest.msg = this.msg;
    } else {
      this.registerRequest.tenantCode = this.tenantCode;
    }
    this.registerRequest.vendorCode = this.selectedProvider['id'] || this.selectedProvider;
    if (this.src === 'CONFINS') {
      this.registerRequest.isRegisterWithoutLogin = '1';
    }

    // Check email service, IF '1' then Skip OTP
    if (this.src === 'INVITATION') {
      this.isEmailFromEsign = !this.userProfile.email;
    } else if (this.src === 'CONFINS') {
      this.isEmailFromEsign = false;
      const urlCheckUserEmailService = URLConstant.checkUserEmailServiceEmbed;
      const request = new CheckUserEmailServiceRequest(this.userProfile.email);
      request.audit = { callerId: this.userProfile.email };
      request.msg = this.msg;
      request.psreCode = this.vendorCode;

      await this.userService.checkUserEmailService(request, urlCheckUserEmailService).toPromise().then(
        (response) => {
          if (response.status.code !== 0) {
            throw response.status.message;
          }
        }
      );

    } else {
      const urlCheckUserEmailService = URLConstant.checkUserEmailService;
      const requestCheckUserEmailService = new CheckUserEmailServiceRequest(this.userProfile.email);
      requestCheckUserEmailService.audit = { callerId: this.userProfile.email };
      await this.userService.checkUserEmailService(requestCheckUserEmailService, urlCheckUserEmailService).toPromise().then(
        (response) => {
          if (response.status.code !== 0) {
            throw response.status.message;
          }
          if (response.emailServiceStatus === '1') {
            this.isEmailFromEsign = true;
            this.registerUser();
          }
        }
      );
    }

    if (this.isEmailFromEsign && this.src === 'INVITATION') {
      this.registerUser();
      return;
    }

    if (!this.isEmailFromEsign) {
      const modal = this.modalService.open(VerificationEmailComponent, { backdrop: 'static', keyboard: false });
      modal.componentInstance.email = this.userProfile.email;
      modal.componentInstance.fullname = this.userProfile.nama;
      modal.componentInstance.vendorCode = this.vendorCode;
      modal.componentInstance.notifType = 'EMAIL';

      if (this.src === 'INVITATION') {
        modal.componentInstance.verificationType = CommonConstant.VERIF_TYPE_INV;
        modal.componentInstance.msg = this.msg;
      }

      modal.result.then(
        (response) => {
          console.log('Verif Email:', response);
          localStorage.removeItem('oldEmail');
          localStorage.removeItem('timeLeft');
          this.registerUser();
        }
      ).catch((error) => { });
    }
  }

  registerUser() {
    let url;

    switch (this.src) {
      case 'CONFINS':
        url = this.registerRequest.userData.psreCode === Vendor.TekenAja ? URLConstant.RegisterUserEmbedTekenAja : URLConstant.RegisterUserEmbed;
        break;
      case 'INVITATION':
        url = URLConstant.RegisterByInvitation;
        break;
      default:
        url = this.registerRequest.userData.psreCode === Vendor.TekenAja ? URLConstant.RegisterUserTekenAja : URLConstant.RegisterUserEmbedV2;
        break;
    }

    this.userService.register(this.registerRequest, url).subscribe(
      (response) => {
        console.log('Response', response);
        if ((response.status.message?.includes('8106') || response.status.code === 8137 || response.status.message?.includes('8127') || response.status.message?.includes('sudah terdaftar') || response.status.message?.includes('already registered')) && this.src === 'INVITATION') {
          response.status.code = 0;

          // Open reregister modal
          if (response?.reregistrationAvailable === '1') {

            const modal = this.ngModal.open(ReregisterModalComponent, { size: 'md', backdrop: 'static', keyboard: false });
            modal.componentInstance.vendorCode = this.vendorCode;
            modal.componentInstance.tenantCode = this.tenantCode;
            modal.componentInstance.invitationCode = this.msg;
            modal.componentInstance.idNo = this.idno;
            modal.componentInstance.email = this.email;
            modal.componentInstance.phone = this.phone;

          } else if (this.selectedProvider.id === Vendor.TekenAja
            && (response.status.message === 'Sertifikat telah diperbarui'
                || response.status.message === 'Anda sudah terdaftar di TekenAja. Silakan verifikasi melalui email yang telah dikirim.')) {
            
            this.openExternalActivationPopup();

          } else {

            const modal = this.ngModal.open(MsxAlertComponent, { size: 'md', backdrop: 'static', keyboard: false });
            modal.componentInstance.image = './assets/img/ic_warning.png';
            modal.componentInstance.title = 'Anda sudah terdaftar';
            modal.componentInstance.message = response.status.message;

          }
          return;

        } else if (this.src === 'CONFINS' && response?.reregistrationAvailable === '1') {

          response.status.code = 0;
          const modal = this.ngModal.open(ReregisterModalComponent, { size: 'md', backdrop: 'static', keyboard: false });
          modal.componentInstance.vendorCode = this.vendorCode;
          modal.componentInstance.tenantCode = this.tenantCode;
          modal.componentInstance.idNo = this.idno;
          modal.componentInstance.embedMsg = this.msg;
          modal.componentInstance.email = this.email;
          modal.componentInstance.phone = this.phone;
          return;

        } else if (response.status.code === 8170) {
          // Handling error code: percobaan maksimum registrasi tercapai
          const modal = this.ngModal.open(MsxAlertComponent, { size: 'md', backdrop: 'static', keyboard: false });
          modal.componentInstance.image = './assets/img/ic_warning.png';
          modal.componentInstance.title = 'Percobaan maksimum registrasi tercapai';
          modal.componentInstance.message = response.status.message;
          return;

        } else if (response.status.code !== 0) {
          // di-comment supaya tidak muncul response error 2 kali
          // this.toastrService.error(response.status.message, null, { positionClass: CommonConstant.TOAST_BOTTOM_RIGHT });
          return;
        }

        // Handling status.code 0

        // Digisign (sudah terdaftar)
        if (this.selectedProvider.id === Vendor.DigiSign && response.status?.message.includes('sudah terdaftar')) {
          this.toastrService.error(response.status.message, null, { positionClass: CommonConstant.TOAST_BOTTOM_RIGHT });
          return;
        }

        // Aktivasi dari halaman Digisign
        // tslint:disable-next-line:max-line-length
        if (this.selectedProvider.id === Vendor.DigiSign) {
            this.activationStage();
        }

        // Kalau response 'Sertifikat telah diperbarui' || 'Anda sudah terdaftar di TekenAja. Silakan verifikasi melalui email yang telah dikirim.'
        if (this.selectedProvider.id === Vendor.TekenAja
              && (response.status.message === 'Sertifikat telah diperbarui'
                  || response.status.message === 'Anda sudah terdaftar di TekenAja. Silakan verifikasi melalui email yang telah dikirim.')) {
          
          this.openExternalActivationPopup();
          return;
        }

        // Aktivasi dari halaman eSignHub
        if (this.selectedProvider.id === Vendor.VIDA || (this.selectedProvider.id === Vendor.TekenAja && this.src === 'INVITATION')) {
          this.goToactivationForm(this.msg, this.registerRequest.vendorCode);
        }
        
        if (this.selectedProvider.id === Vendor.TekenAja) {
          this.state = 'register-success';
          this.registerMsg = response.status?.message;
          this.cdr.detectChanges();
  
          this.toastrService.success(`Registrasi berhasil silahkan lanjutkan proses aktivasi.`, null, {
            positionClass: 'toast-top-right'
          });
        }

        if (this.selectedProvider.id === Vendor.PRIVY) {
          const modal = this.modalService.open(MsxAlertComponent, { backdrop: 'static', keyboard: false, size: 'm' });
            modal.componentInstance.message = 'Proses verifikasi anda sedang diproses. Harap menunggu proses verifikasi selesai.';
            modal.componentInstance.hideBtn = true;
        }

      }, error => {
        this.toastrService.error(error.message, null, { positionClass: CommonConstant.TOAST_BOTTOM_RIGHT });
      });
  }

  goToactivationForm(message:any, vendor:any) {
    this.router.navigate([PathConstant.ACTIVATION_FORM], {state: {msg: message, vendor: vendor}});
  }

  activationStage() {
    if (this.src === 'CONFINS') {
      let user: UserProfile;
      if (this.global.user == null) {
        user = new UserProfile();
      } else {
        user = this.global.user
      }
      user.pathSrc = CommonConstant.PATH_SRC_EMBED;
      this.global.user = user;
      this.router.navigate([PathConstant.EMBED_ACTIVATION], {
        queryParams:
          { uri: this.userProfile.email, msg: this.msg, vendorCode: this.selectedProvider['id'] || this.selectedProvider }
      });
    } else if (this.src === 'INVITATION') {
      const user: UserProfile = new UserProfile();
      user.pathSrc = CommonConstant.PATH_SRC_INV;
      this.global.user = user;
      const actParams  = {status: 1, params: { src: this.src, uri: this.userProfile.email, msg: this.msg, vendorCode: this.selectedProvider['id'] || this.selectedProvider }};
      localStorage.setItem(this.msg, JSON.stringify(actParams));
      this.gotoActInv(actParams.params);
    } else {
      this.router.navigate([PathConstant.ACTIVATION], {
        queryParams:
          { uri: this.userProfile.email, tenantCode: this.tenantCode, vendorCode: this.selectedProvider['id'] || this.selectedProvider }
      });
    }
  }

  gotoActInv(params) {
    if(params.vendorCode == 'DIGI'){
    this.router.navigate([PathConstant.INVITATION_ACTIVATION], {
      queryParams:
      params
    });
    } else if(params.vendorCode == Vendor.VIDA || params.vendorCode == Vendor.PRIVY || params.vendorCode == Vendor.TekenAja){
      this.goToactivationForm(this.msg, params.vendorCode);
    }
  }

  onAnswerChange() {
    console.log('Answer', this.selectedAnswer);
    this.cdr.detectChanges();
  }

  onFileChange(fileInput: any, key: string) {
    console.log('input-ktp', 'change');
    if (fileInput.target.files.length > 0) {
      this.fileName = fileInput.target.files[0].name;
      const img = new Image();
      const readerImg = new FileReader();
      console.log('Photo file', fileInput.target.files[0]);
      readerImg.readAsDataURL(fileInput.target.files[0]);
      readerImg.onload = (e: any) => {
        const imgSrc = e.target.result.replace(CommonConstant.DATA_IMAGE_JPG_BASE64, CommonConstant.DATA_IMAGE_JPEG_BASE64);
        this.ngZone.run(() => {
          this.processImg(imgSrc, fileInput.target.files[0].size, key);
        })
      };
    }
    setTimeout(() => {
      this.cdr.detectChanges();
    }, 5);
  }

  clearFotoKtp(){
    this.registerForm.get('fotoKtpRaw').setValue('');
    this.fileName = null;
    this.imageName = null;
    this.imageChangedEvent = null;
    this.imgState = null;
    this.cdr.detectChanges();
  }
  
  dataURItoBlob(dataURI) {
    const byteString = window.atob(dataURI);
    const arrayBuffer = new ArrayBuffer(byteString.length);
    const int8Array = new Uint8Array(arrayBuffer);
    for (let i = 0; i < byteString.length; i++) {
      int8Array[i] = byteString.charCodeAt(i);
    }
    const blob = new Blob([int8Array], { type: CommonConstant.IMAGE_TYPE_JPEG });    
    return blob;
  }

  openWebcam(name: string) {
    const prop = name;
    if (this.psreCode == Vendor.PRIVY) {
      const modal = this.modalService.open(PrivyLivenessComponent, {size: 'fullscreen', centered: true, backdrop: 'static', keyboard: false});
      modal.componentInstance.livenessUrl = this.urlLivenessPrivy;
      modal.result.then(livenessResult => {
        const imageName = 'test.jpeg';
        const imageBlob = this.dataURItoBlob(livenessResult.replace(CommonConstant.DATA_IMAGE_JPEG_BASE64, ''));
        const imageFile = new File([imageBlob], imageName, { type: CommonConstant.IMAGE_TYPE_JPEG });
        
        this.processImg(livenessResult, imageFile.size, 'fotoSelfie');
        this.cdr.detectChanges();
      });
    } else {
      this.modalService.open(CameraComponent).dismissed.subscribe(result => {
        const content = result.image.split(',')[1];
        const size = Math.round((content.length) * 3 / 4);
        this.processImg(result.image, size, name);
        this.cdr.detectChanges();
        console.log('Self Image', prop);
      })
    }
  }

  openWebcamKtp(name: string) {
    const prop = name;
    this.modalService.open(CameraKtpComponent).dismissed.subscribe(result => {
      const content = result.image.split(',')[1];
      const size = Math.round((content.length) * 3 / 4);
      this.processImg(result.image, size, name);
      this.cdr.detectChanges();

      if (prop === 'fotoKtp') {
        this.registerForm.get(prop).setValue(null);
      }
      console.log('KTP Image', prop);
    })
  }

  processImg(imgSrc: any, size: number, key: string) {
    this.imageName = key;
    this.imageChangedEvent = imgSrc;
    this.imgState = {size: size, key: key};
    this.cdr.detectChanges();
  }

  onNewImg(height: any, width: any, img: any): any {
    const canvas: HTMLCanvasElement = document.createElement('canvas');
    const ctx: any = canvas.getContext('2d');
    ctx.drawImage(img, 0, 0);
    const MAX_WIDTH_HEIGHT = CommonConstant.MAX_IMAGE_WIDTH_HEIGHT;
    if (width > height) {
      if (width > MAX_WIDTH_HEIGHT) {
        height *= MAX_WIDTH_HEIGHT / width;
        width = MAX_WIDTH_HEIGHT;
      }
    } else if (width < height) {
      if (height > MAX_WIDTH_HEIGHT) {
        width *= MAX_WIDTH_HEIGHT / height;
        height = MAX_WIDTH_HEIGHT;
      }
    } else {
      width = MAX_WIDTH_HEIGHT;
      height = MAX_WIDTH_HEIGHT;
    }
    canvas.width = width;
    canvas.height = height;
    const ctx1 = canvas.getContext('2d');
    ctx1.drawImage(img, 0, 0, width, height);
    const newImage = canvas.toDataURL(CommonConstant.IMAGE_TYPE_JPEG);
    const blobBin = atob(newImage.split(',')[1]);
    const array = [];
    for (let i = 0; i < blobBin.length; i++) {
      array.push(blobBin.charCodeAt(i));
    }
    const newBlob = new Blob([new Uint8Array(array)], {type: CommonConstant.IMAGE_TYPE_JPEG});
    return newBlob;
  }

  onInput($event) {
    console.log($event);
    console.log('Form', this.registerForm);
  }

  onTnc($event) {
    console.log('Form', this.registerForm);
  }

  parseDate(strDate: any) {
    // previous code -> moment(strDate, 'DD/MM/YYYY').format('YYYY-MM-DD');
    if (!strDate) {
      return null;
    }
    const date = moment(strDate).format('YYYY-MM-DD');
    console.log('Formatted', date);
    return date;
  }

  private setProperties() {
    // Message Template
    this.template = {
      required: '{x} harus diisi',
      maxlength: 'Maksimal jumlah karakter {x} adalah {v}',
      minlength: 'Minimal jumlah karakter {x} adalah {v}',
      pattern: 'Silahkan isi {x} dengan format yang benar.',
      agreement: '{x} harus disetujui',
    };

    this.fields = {
      name: {
        label: 'Nama Lengkap',
        prop: {
          maxlength: 50
        }
      },
      nik: {
        label: 'NIK',
        prop: {
          maxlength: 16
        }
      },
      pob: {
        label: 'Tempat Lahir',
        prop: {
          maxlength: 50
        }
      },
      dob: {
        label: 'Tanggal Lahir'
      },
      gender: {
        label: 'Jenis Kelamin'
      },
      phone: {
        label: 'No. Telp',
        prop: {
          maxlength: 15
        }
      },
      address: {
        label: 'Alamat',
        prop: {
          maxlength: 150
        }
      },
      subDistrict: {
        label: 'Kelurahan',
        prop: {
          maxlength: 50
        }
      },
      district: {
        label: 'Kecamatan',
        prop: {
          maxlength: 50
        }
      },
      city: {
        label: 'Kota',
        prop: {
          maxlength: 50
        }
      },
      province: {
        label: 'Provinsi',
        prop: {
          maxlength: 50
        }
      },
      zip: {
        label: 'Kode Pos',
        prop: {
          maxlength: 5
        }
      },
      fotoSelfie: {
        label: 'Foto Diri'
      },
      fotoKtp: {
        label: 'Foto KTP'
      },
      email: {
        label: 'Email',
        prop: {
          maxlength: 64
        }
      },
      acceptTerms: {
        label: 'Syarat dan Ketentuan'
      },
      acceptTermsMitra: {
        label: 'Syarat dan Ketentuan Mitra'
      },
      acceptTermsVida: {
        label: 'Syarat dan Ketentuan PSrE'
      },
    };

    // Validation Custom Error Messages
    this.validationMessages = {
      name: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.name.label) },
        {
          type: 'maxlength', message: this.template.maxlength.replace('{x}', this.fields.name.label)
            .replace('{v}', this.fields.name.prop.maxlength)
        },
      ],
      nik: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.nik.label) },
        {
          type: 'maxlength', message: this.template.maxlength.replace('{x}', this.fields.nik.label)
            .replace('{v}', this.fields.nik.prop.maxlength)
        },
        { type: 'pattern', message: this.template.pattern.replace('{x}', this.fields.nik.label) },
      ],
      pob: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.pob.label) },
        {
          type: 'maxlength', message: this.template.maxlength.replace('{x}', this.fields.pob.label)
            .replace('{v}', this.fields.pob.prop.maxlength)
        },
      ],
      dob: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.dob.label) }
      ],
      gender: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.gender.label) }
      ],
      phone: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.phone.label) },
        {
          type: 'maxlength', message: this.template.maxlength.replace('{x}', this.fields.phone.label)
            .replace('{v}', this.fields.phone.prop.maxlength)
        },
        { type: 'pattern', message: this.template.pattern.replace('{x}', this.fields.phone.label) },
      ],
      address: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.address.label) }
      ],
      subDistrict: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.subDistrict.label) },
        {
          type: 'maxlength', message: this.template.maxlength.replace('{x}', this.fields.subDistrict.label)
            .replace('{v}', this.fields.subDistrict.prop.maxlength)
        },
      ],
      district: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.district.label) },
        {
          type: 'maxlength', message: this.template.maxlength.replace('{x}', this.fields.district.label)
            .replace('{v}', this.fields.district.prop.maxlength)
        },
      ],
      city: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.city.label) },
        {
          type: 'maxlength', message: this.template.maxlength.replace('{x}', this.fields.city.label)
            .replace('{v}', this.fields.city.prop.maxlength)
        },
      ],
      province: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.province.label) },
        {
          type: 'maxlength', message: this.template.maxlength.replace('{x}', this.fields.province.label)
            .replace('{v}', this.fields.province.prop.maxlength)
        },
      ],
      zip: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.zip.label) },
        {
          type: 'maxlength', message: this.template.maxlength.replace('{x}', this.fields.zip.label)
            .replace('{v}', this.fields.zip.prop.maxlength)
        },
        { type: 'pattern', message: this.template.pattern.replace('{x}', this.fields.zip.label) },
      ],
      psre: [
        { type: 'required', message: this.template.required.replace('{x}', 'PSRe') }
      ],
      fotoSelfie: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.fotoSelfie.label) }
      ],
      fotoKtp: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.fotoKtp.label) }
      ],
      email: [
        { type: 'required', message: this.template.required.replace('{x}', this.fields.email.label) },
        {
          type: 'maxlength', message: this.template.maxlength.replace('{x}', this.fields.email.label)
            .replace('{v}', this.fields.email.prop.maxlength)
        },
      ],
      acceptTerms: [
        { type: 'required', message: this.template.agreement.replace('{x}', this.fields.acceptTerms.label) }
      ],
      acceptTermsMitra: [
        { type: 'required', message: this.template.agreement.replace('{x}', this.fields.acceptTermsMitra.label) }
      ],
      acceptTermsVida: [
        { type: 'required', message: this.template.agreement.replace('{x}', this.fields.acceptTermsVida.label) }
      ],
    };
  }

  getFileSize(key: string) {
    return this.sizeMap[key];
  }

  goToDetail() {
    this.router.navigate(['inquiry', 'detail'], { queryParams: { contractRef: '0002AG202' } });
  }

  imageCropped(event: CroppedEvent) {
    this.base64 = event.base64;
    // this.registerForm.get(this.imageName).setValue(this.base64.replace(CommonConstant.DATA_IMAGE_JPG_BASE64, CommonConstant.DATA_IMAGE_JPEG_BASE64));
    this.imageName = null;
    this.imageChangedEvent = null;
    const img = new Image();
    img.src = event.base64;
    img.onload = (ev) => {
      if (event.file.size.valueOf() >= 500000) {
        const result = this.onNewImg(img.naturalHeight, img.naturalWidth, img);
        const readerPhoto = new FileReader();
        readerPhoto.readAsDataURL(result);
        readerPhoto.onload = (e: any) => {
          const resultX = e.target.result;
          const content = resultX.split(',')[1];
          const sizeX = Math.round((content.length) * 3 / 4);

          console.log('image compress', resultX);
          // Validate size length kembali.
          while (sizeX > 500000) {
            this.processImg(resultX, sizeX, this.imgState.key);
          }

          this.registerForm.get(this.imgState.key).setValue(resultX.replace(CommonConstant.DATA_IMAGE_JPG_BASE64, CommonConstant.DATA_IMAGE_JPEG_BASE64));
          this.sizeMap[this.imgState.key] = this.formatFileSize(sizeX);
          console.log('Original Image', e.target.result);
          console.log(`File Size ${this.imgState.key}`, this.sizeMap[this.imgState.key]);
          this.spinner.hide();
        };
      } else {
        this.registerForm.get(this.imgState.key).setValue(this.base64.replace(CommonConstant.DATA_IMAGE_JPG_BASE64, CommonConstant.DATA_IMAGE_JPEG_BASE64));
        this.sizeMap[this.imgState.key] = this.formatFileSize(this.imgState.size);
        console.log(`File Size ${this.imgState.key}`, this.sizeMap[this.imgState.key]);
        console.log('Original Image', event.base64);
        this.spinner.hide();
      }
    };
    console.log('Crop Image Output', this.base64);
  }

  formatFileSize(bytes, si: boolean = false, dp = 1) {
    const thresh = si ? 1000 : 1024;

    if (Math.abs(bytes) < thresh) {
      return bytes + ' B';
    }

    const units = si
      ? ['kB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
      : ['KiB', 'MiB', 'GiB', 'TiB', 'PiB', 'EiB', 'ZiB', 'YiB'];
    let u = -1;
    const r = 10 ** dp;

    do {
      bytes /= thresh;
      ++u;
    } while (Math.round(Math.abs(bytes) * r) / r >= thresh && u < units.length - 1);


    return bytes.toFixed(dp) + ' ' + units[u];
  }

  getFormValidationErrors() {
    console.log('%c ==>> Validation Errors: ', 'color: red; font-weight: bold; font-size:25px;');
    this.messages = [];
    this.keys = [];
    return Object.keys(this.registerForm.controls).forEach(async key => {
      const controlErrors: ValidationErrors = this.registerForm.get(key).errors;
      if (controlErrors != null) {
        Object.keys(controlErrors).forEach(keyError => {
          const msg = this.validationMessages[key].find(x => x.type === keyError);
          this.registerForm.get(key).markAsDirty();
          this.messages.push(msg.message);
          this.keys.push(key);
          console.log('msg', this.messages);
          console.log('Key control: ' + key + ', keyError: ' + keyError + ', err value: ', controlErrors[keyError]);
        });

        this.setFocus(this.keys[0]);
      }
    });
  }

  setFocus(name) {
    const ele = this.formElement.nativeElement[name];
    if (ele) {
      ele.focus();
    }
  }

  onChange(prop: string, $event) {
    console.log(prop, $event);

    switch (prop) {
      case this.formFields.psre:
        console.log('Provider', this.provider);
        this.selectedProvider = $event;
        this.checkComboModeProvince($event);
      break;

      case this.formFields.province:
        this.getCity(this.comboMode);
      break;

      case this.formFields.city:
        this.getDistrict();
        break;
    }

    console.log('selectedProvide', this.selectedProvider);
  }

  checkComboModeProvince(psre: {id: string, name: string}) {
    this.comboMode = psre.id === Vendor.TekenAja && this.src !== 'INVITATION';
    console.log('comboMode', this.comboMode);

    this.getProvince(this.comboMode);
  }

  getProvince(isComboMode: boolean) {
    if (!isComboMode) {
      // Revert value to default input
      this.registerForm.patchValue({
        province: this.tmpUser?.provinsi,
        city: this.tmpUser?.kota,
        district: this.tmpUser?.kecamatan,
        subDistrict: this.tmpUser?.kelurahan
      });

      // Revert selected data
      this.selectedProvince = this.tmpUser?.provinsi;

      this.disableRegisterInput();
      return;
    } else {
      console.log('comboMode', 'TekenAJa');
    }

    const matchProvinceValue = () => {
      console.log('matchProvince', true);
      const checkProvince = this.listProvince.find(x => x.namaProvinsi === this.global.user?.provinsi);
      console.log('checkProvince', checkProvince);

      if (checkProvince) {
        this.selectedProvince = checkProvince;
        console.log('selectedProvince', this.selectedProvince);
        this.comboProvince = checkProvince;
        console.log('comboProvince', this.comboProvince);
        this.getCity(isComboMode);
      } else {
        this.selectedProvince = null;
        this.comboProvince = null;
        this.registerForm.controls['province'].enable();
      }
    };

    // if (this.listProvince) {
    //   matchProvinceValue();
    //   console.log('invalidate province', this.selectedProvince);
    //   return;
    // }

    if (this.src === '') {
      console.log('tmpUser', this.tmpUser);
      const request = new DataRequest();
      request.provinceName = this.global.user.provinsi;

      this.dataService.getProvince(request).subscribe(res => {
        this.listProvince = res?.provinceList;
        console.log(CommonConstant.LIST_PROVINCE, this.listProvince);
        matchProvinceValue();
      })
    } else if (this.src === 'INVITATION') {
      const request = new DataRequest();
      request.msg = this.msg;

      this.dataService.getProvinceByInv(request).subscribe(res => {
        this.listProvince = res?.provinceList;
        console.log(CommonConstant.LIST_PROVINCE, this.listProvince);
        matchProvinceValue();
      })
    } else if (this.src === 'CONFINS') {
      const request = new DataRequest();
      request.provinceName = this.userProfile?.provinsi || '';
      request.msg = this.msg;

      this.dataService.getProvinceEmbed(request).subscribe(res => {
        this.listProvince = res?.provinceList;
        console.log(CommonConstant.LIST_PROVINCE, this.listProvince);
        matchProvinceValue();
      })
    }
  }

  getCity(isComboMode: boolean) {
    console.log('Select City', true);
    const matchCityValue = () => {
      const checkCity = this.listCity.find(x => x.districtName === this.global.user?.kota);
      console.log('checkCity', checkCity);
      if (checkCity) {
        this.selectedCity = checkCity;
        this.registerForm.controls['city'].disable();
        this.getDistrict();
      } else {
        this.selectedCity = null;
        this.registerForm.controls['city'].enable();
      }
    };

    if (this.src === '') {
      const request = new DataRequest();
      request.districtName = this.global.user.kota;
      // request.provinceId = this.selectedProvince['idMsProvinsi'];

      this.dataService.getCity(request).subscribe(res => {
        this.listCity = res?.listDistrict;
        console.log('List City', this.listCity);
        matchCityValue();
      })
    } else if (this.src === 'INVITATION') {
      const request = new DataRequest();
      request.msg = this.msg;
      request.provinceId = this.selectedProvince['idMsProvinsi'];

      this.dataService.getCityByInv(request).subscribe(res => {
        this.listCity = res?.listDistrict;
        console.log('List City', this.listCity);
        matchCityValue();
      })
    } else if (this.src === 'CONFINS') {
      const request = new DataRequest();
      request.msg = this.msg;
      request.districtName = this.global.user?.kota || '';
      request.provinceId = this.selectedProvince['idMsProvinsi'];

      this.dataService.getCityEmbed(request).subscribe(res => {
        this.listCity = res?.listDistrict;
        console.log('List City', this.listCity);
        matchCityValue();
      })
    }
  }

  getDistrict(isComboMode?: boolean) {
    if (!this.selectedCity) {
      this.listDistrict = [];
      this.selectedDistrict = null;
      return;
    }

    const matchDistrictValue = () => {
      const checkDistrict = this.listDistrict.find(x => x.subDistrictName === this.global.user?.kecamatan);
      console.log('checkDistrict', checkDistrict);

      if (checkDistrict) {
        this.selectedDistrict = checkDistrict;
        this.registerForm.controls['district'].disable();
      } else {
        this.selectedDistrict = null;
        this.registerForm.controls['district'].enable();
      }
    };

    if (this.src === '') {
      const request = new DataRequest();
      request.districtId = this.selectedCity['idMsDistrict'];
      request.subDistrictName = this.global.user.kecamatan;

      this.dataService.getDistrict(request).subscribe(res => {
        this.listDistrict = res?.listSubDistrict;
        console.log(CommonConstant.LIST_DISTRICT, this.listDistrict);
        matchDistrictValue();
      })
    } else if (this.src === 'INVITATION') {
      const request = new DataRequest();
      request.msg = this.msg;
      request.districtId = this.selectedCity['idMsDistrict'];

      this.dataService.getDistrictByInv(request).subscribe(res => {
        this.listDistrict = res?.listSubDistrict;
        console.log(CommonConstant.LIST_DISTRICT, this.listDistrict);
        matchDistrictValue();
      })
    } else if (this.src === 'CONFINS') {
      const request = new DataRequest();
      request.msg = this.msg;
      request.districtId = this.selectedCity['idMsDistrict'];
      request.subDistrictName = this.global.user?.kecamatan || '';

      this.dataService.getDistrictEmbed(request).subscribe(res => {
        this.listDistrict = res?.listSubDistrict;
        console.log(CommonConstant.LIST_DISTRICT, this.listDistrict);
        matchDistrictValue();
      })
    }
  }

  getContact() {
    return this.userProfile.email || this.userProfile.tlp;
  }

  openExternalActivationPopup() {
    const modal = this.modalService.open(MsxAlertComponent, { backdrop: 'static', keyboard: false, size: 'm' });
    const messageMedia = this.registerForm.getRawValue().email ? 'Email' : 'SMS';
    const modalMessage = 'Harap lanjutkan proses aktivasi Anda menggunakan link yang dikirimkan melalui ' + messageMedia + '.';
    modal.componentInstance.message = modalMessage;
    modal.componentInstance.hideBtn = true;
  }

}
