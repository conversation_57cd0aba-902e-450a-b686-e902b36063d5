import { HttpClient } from '@angular/common/http';
import { Component, Input, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { checkLivenessFaceCompareRequest } from 'app/model/api/check-liveness-face-compare.request';
import { signerDataVerificationRequest } from 'app/model/api/signer-data-verification.request';
import { AuditContext } from 'app/model/audit.context';
import { CameraLivenessComponent } from 'app/shared/components/camera/camera-liveness/camera-liveness.component';
import { URLConstant } from 'app/shared/constant/URLConstant';
import { GlobalService } from 'app/shared/data/global.service';
import { SentOtpSigningVerificationRequest } from 'app/shared/dto/otp/sent-otp-signing-verification.request';
import { ToastrService } from 'ngx-toastr';
import { SignerSigningOtpVerificationComponent } from '../../signer-signing-otp-verification/signer-signing-otp-verification.component';
import { BaseResponse } from 'app/model/api/base.response';
import * as swalFunction from '../../../../shared/data/sweet-alerts';
import { Router } from '@angular/router';
import { DeviceDetectorService } from 'ngx-device-detector';
import { Document } from 'app/model/document';
import { CountlyService } from 'app/services/api/countly.service';
import { Vendor } from 'app/shared/constant/vendor';
import { GetAvailableOtpSendingPointsRequest } from 'app/model/api/get-available-otp-sendingpoints.request';
import { TenantService } from 'app/services/api/tenant.service';

@Component({
  selector: 'app-signer-signing-verification',
  templateUrl: './signer-signing-verification.component.html',
  styleUrls: ['./signer-signing-verification.component.scss'] 
})
export class SignerSigningVerificationComponent implements OnInit {
  verificationForm: FormGroup;
  isShowPassword = false;
  isChecked = false;
  biometricVisible = true;
  BioChecked = false;
  OTPChecked = false;
  isMobile = false;
  swal = swalFunction;
  @Input() email: string;
  @Input() phoneNo: string;
  @Input() vendorCode: string;
  @Input() msg: string;
  @Input() tenantCode: string;
  @Input() documents : Document[];
  @Input() maxLivenessFaceCompareAttempt: string;
  @Input() isNoPassword : string;
  fields: any;
  live = 0;
  default:any;
  mustLivenessFaceCompareFirst :any;
  isOtp: boolean = true;
  isSms: boolean;
  isWa: boolean;
  isEmail: boolean;
  isEmbed: boolean;
  isPrivy: boolean;
  
  constructor(public activeModal: NgbActiveModal, private fb: FormBuilder, private http: HttpClient,private toastrService: ToastrService,
    private modalService: NgbModal, private global: GlobalService, private router: Router,private deviceService: DeviceDetectorService,private countlyService: CountlyService,
    private tenantService: TenantService
    ) { 
    this.default = 'OTP'

    if(deviceService.isMobile()){
      this.isMobile = true;
      console.log('This is a mobile device!');
    }
  }

  async ngOnInit(): Promise<void> {
    this.checkLivenessFaceCompareService();
    await this.initView();
  }

  onClickBio() {
    this.OTPChecked = false;
    this.default = 'Biometric'
    this.isOtp = false;
  }

  onClickOTP() {
    this.BioChecked = false;
    this.default = 'OTP'
    this.isOtp = true;
  }

  toggleIsShowPassword() {
    this.isShowPassword = !this.isShowPassword;
  }

  dismiss() {
    this.activeModal.dismiss('0');
  }

  checkLivenessFaceCompareService(){
    if (this.vendorCode === Vendor.PRIVY) {
      this.live = 0;
    } else {
      const request: checkLivenessFaceCompareRequest = new checkLivenessFaceCompareRequest();
      request.audit = new AuditContext();
  
      let url: string;
      if (this.msg) {
        request.tenantCode = this.tenantCode;
        request.msg = this.msg;
        url = URLConstant.CheckLivenessFaceCompareEmbedV2;

        // this.http.post(url,request).subscribe((res) => {
        //   if(res['status']['code'] === 0) {
            
        //     if(res['livenessFacecompareServicesStatus'] === '1') {
        //       this.live = 2
        //       this.default = 'Biometric' 
        //     } else {
        //       this.live = 0
        //     }
        //   }
        // })
      } else {
        request.tenantCode = this.global.user.role.tenantCode;
        request.audit.callerId = this.email;
        url = URLConstant.CheckLivenessFaceCompare;
      }

      this.http.post(url,request).subscribe((res) => {
        if(res['status']['code'] === 0) {
          
          this.mustLivenessFaceCompareFirst = res['mustLivenessFaceCompareFirst'];
          if(res['livenessFacecompareServicesStatus'] === '1'&& this.maxLivenessFaceCompareAttempt === '0' 
          && this.mustLivenessFaceCompareFirst === '1') {
            this.live = 1
          }
          else if (this.mustLivenessFaceCompareFirst === '1' && res['livenessFacecompareServicesStatus'] === '1' 
          && this.maxLivenessFaceCompareAttempt === '1'){
            this.live = 0
          }
          else if(res['livenessFacecompareServicesStatus'] === '1' && this.mustLivenessFaceCompareFirst=== '0'){
            this.live = 2
            this.default = 'Biometric'
            this.isOtp = false;
          }
          else {
            this.live = 0
          }

          if(this.live == 1){
            this.default = 'Biometric' 
            this.isOtp = false;
        } 
        }
      })
    }
  }

  async initView(){
    this.isPrivy = this.vendorCode === Vendor.PRIVY;
    this.isEmbed = this.msg && this.router.url.includes('/embed/') &&  this.router.url.includes('/V2/');
    this.verificationForm = this.fb.group({
      Email: ['', Validators.compose([
        Validators.required,
        Validators.maxLength(64)]),
      ],
      PhoneNo: ['', Validators.compose(
        [Validators.required, 
         Validators.minLength(10)]) ],
         
      password:  ['', this.isNoPassword === '0' ? Validators.compose([
        Validators.required, 
        Validators.maxLength(50)]) : []],

      verification: ['', Validators.compose(
        [Validators.required])],

      check: ['false', Validators.requiredTrue],

      
      sendMedia: (this.isEmbed) ? [] : ['', Validators.compose(
          [Validators.required])]
    })

    this.verificationForm.patchValue({
      Email: this.email,
      PhoneNo: this.phoneNo
    })

    this.fields = {
      Email: {
        label: 'Email',
        prop: {
          maxlength: 64
        }
      },
      PhoneNo: {
        label: 'No Handphone',
        prop: {
          maxlength: 20
        }
      },
      password: {
        label: 'Kata Sandi',
        prop: {
          maxlength: 50,
          minlength: 8
        }
      },
      check:{
      },
      sendMedia: {
        label: (!this.isEmbed) ? 'Media Pengiriman OTP' : ''
      }
    }
    await this.getAvailableSendingPoint();
  }

  async getAvailableSendingPoint() {
    let getAvailableSendingPointUrl;
    const request = new GetAvailableOtpSendingPointsRequest();
    request.tenantCode = this.tenantCode;
    request.vendorCode = this.vendorCode;

    if (this.isEmbed) {
      getAvailableSendingPointUrl = URLConstant.getAvailableSendingOptionsEmbedV2;
      request.msg = this.msg;
    }else {
      getAvailableSendingPointUrl = URLConstant.GetAvailableSendingPoint;
      request.loginId = this.email;
    }

    this.tenantService.getAvailableSendingPointSecured(request, getAvailableSendingPointUrl).subscribe(
      response => {
        if (response.status.code === 0) {
          const available = response.listAvailableOptionSendingPoint;
          if(available.length > 1) {
            if (available.includes('SMS')) {
              this.isSms = true;
            }
  
            if (available.includes('WA')) {
              this.isWa = true;
            }

            if (available.includes('EMAIL')) {
              this.isEmail = true;
            }
  
            this.verificationForm.patchValue({
              sendMedia: response.defaultAvailableOptionSendingPoint
            });
          } else {
            if (available[0] === 'SMS') {
              this.isSms = true;
            } else if (available[0] === 'WA') {
              this.isWa = true;
            } else {
              this.isEmail = true;
            }
            this.verificationForm.patchValue({
              sendMedia: available[0]
            });
          }
        }
      }
    )
  }

  signerVerification() {
    const verif: signerDataVerificationRequest = new signerDataVerificationRequest();
    const formData = this.verificationForm.getRawValue();
    verif.audit = new AuditContext();
    verif.tenantCode = this.tenantCode;
    verif.audit.callerId = this.email;

    console.log('data' , formData);
    let url: string;

    if (this.msg && this.router.url.includes('/embed/') &&  this.router.url.includes('/V2/')) {
      url = URLConstant.signerDataVerificationEmbedV2;
      verif.msg = this.msg;

      if (this.isNoPassword === '0') {
        verif.password = formData.password;
      
        this.http.post(url,verif).subscribe((res) => {
          if (res['status']['code'] !== 0 ) {
            return;
          }
          this.verifUser(formData);
        });
      } else {
        this.verifUser(formData);
        console.log('wpe');
      }

    } else {

      verif.email = this.email;
      verif.password = formData.password;
      url = URLConstant.signerDataVerification;

      if (this.isNoPassword === '0') {
        verif.password = formData.password;
        this.http.post(url,verif).subscribe((res) => {
          if (res['status']['code'] !== 0 ) {
            return;
          }
          this.verifUser(formData);
        });
      } else {
        this.verifUser(formData);
        console.log('wpe');
      }
    }
  }

  verifUser(formData) {
    // Send OTP
    if (formData.verification === 'OTP') {
      let urlOtp: string;
      const request = new SentOtpSigningVerificationRequest();
      
      if (this.msg && this.router.url.includes('/embed/') &&  this.router.url.includes('/V2/')) {
        request.phoneNo = this.phoneNo;
        request.vendorCode = this.vendorCode;
        request.tenantCode = this.tenantCode;
        request.msg = this.msg;
        request.sendingPointOption = this.isPrivy ? 'SMS' : formData.sendMedia;
        urlOtp = URLConstant.sentOtpSigningVerificationEmbedV2;
      } else {
        request.phoneNo = this.phoneNo;
        request.vendorCode = this.vendorCode;
        request.tenantCode = this.global.user.role.tenantCode;
        request.sendingPointOption = this.isPrivy ? 'SMS' : formData.sendMedia;
        urlOtp = URLConstant.sentOtpSigningVerification;
      }
      
      request.documentId = [];
      for (const doc of this.documents) {
        request.documentId.push(doc.documentId);
      }

      this.http.post<BaseResponse>(urlOtp, request).subscribe((response) => {
        if (response.status.code !== 0) {
          this.swal.Error(response.status.message);
          return;
        }
        
        const modal = this.modalService.open(SignerSigningOtpVerificationComponent, { backdrop: 'static', keyboard: false, size: 'l' });
        modal.componentInstance.phoneNo = this.phoneNo;
        modal.componentInstance.vendorCode = this.vendorCode;
        modal.componentInstance.loginId = this.email;
        modal.componentInstance.documents = this.documents;
        modal.componentInstance.msg = this.msg;
        modal.componentInstance.tenantCode = this.tenantCode;
        modal.componentInstance.otpByEmail = response['otpByEmail'];
        modal.componentInstance.isWa = this.isWa;
        modal.componentInstance.isSms = this.isSms;
        modal.componentInstance.isEmail = this.isEmail;
        modal.componentInstance.isEmbed = this.isEmbed;
        modal.componentInstance.sendMedia = formData.sendMedia;
        modal.componentInstance.isPrivy = this.isPrivy;
        modal.componentInstance.durationResendOTP = response['durationResendOTP'];
        this.countlyService.trackPageViewData('SignerSignOtpVerification');
        this.activeModal.close();
          
      });
        
    } else if (formData.verification === 'Biometric') {
      // Open liveness face compare modal
      const modal = this.modalService.open(CameraLivenessComponent, { backdrop: 'static', keyboard: false, size: 'l' });
      modal.componentInstance.email = this.email;
      modal.componentInstance.vendorCode = this.vendorCode;
      modal.componentInstance.tenantCode = this.global.user.role.tenantCode;
      modal.componentInstance.documents = this.documents;
      modal.componentInstance.msg = this.msg;
      modal.componentInstance.tenantCode = this.tenantCode;
      modal.componentInstance.phoneNo = this.phoneNo;
      modal.componentInstance.mustLivenessFaceCompareFirst = this.mustLivenessFaceCompareFirst;
      modal.componentInstance.sendingPointOption = this.isPrivy ? 'SMS' : formData.sendMedia;

      this.countlyService.trackPageViewData('CameraLivenessVerification');
      this.activeModal.close(); 
    }
  }
}
